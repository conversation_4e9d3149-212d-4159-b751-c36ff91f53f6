{"name": "rhino-next", "private": true, "type": "module", "scripts": {"analyze": "ANALYZE=true yarn workspace core build", "build": "turbo run build", "dev": "turbo run dev", "dev:staging": "turbo run dev:staging", "dev:msw": "turbo run dev:msw", "dev:core": "yarn workspace core dev", "dev:staging:core": "yarn workspace core dev:staging", "dev:luckyone": "yarn workspace luckyone dev", "dev:luckytwo": "yarn workspace luckytwo dev", "start:core": "yarn workspace core start", "start:luckyone": "yarn workspace luckyone start", "start:luckytwo": "yarn workspace luckytwo start", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "clear-cache": "turbo run clear-cache --filter=core --filter=luckyone --filter=luckytwo && rm -rf .next", "check-types": "turbo run check-types", "check-duplicated-code": "npx jscpd . --exitCode 1", "api:start": "api-start", "postinstall": "husky || true", "generate-heroui-config": "node ./scripts/style-dictionary/heroui/generate-heroui-config.mjs", "build-style-dictionary": "node ./scripts/build-style-dictionary.mjs", "generate-types-for-apis": "node ./scripts/generate-types-for-apis.mjs", "install-recommended-extensions": "cat .vscode/extensions.json | jq -r '.recommendations[]' | xargs -L 1 code --install-extension", "test": "turbo run test", "test:core": "yarn workspace core test", "storybook:core": "yarn workspace core storybook", "storybook:luckyone": "yarn workspace luckyone storybook", "storybook:luckytwo": "yarn workspace luckytwo storybook", "storybook:all": "yarn storybook:core & yarn storybook:luckyone & yarn storybook:luckytwo", "storybook:experimental": "storybook dev -p 6009 -c .storybook"}, "devDependencies": {"@emotion/is-prop-valid": "^1.3.1", "@next/bundle-analyzer": "^15.3.3", "@repo/eslint-config": "*", "@repo/stylelint-plugin-autofix": "*", "@repo/typescript-config": "*", "@storybook/addon-onboarding": "^9.0.4", "@storybook/cli": "^9.0.4", "@storybook/nextjs": "^9.0.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@tokens-studio/sd-transforms": "^2.0.0", "@types/jest": "^30.0.0", "@types/js-cookie": "^3.0.6", "@types/jsonwebtoken": "^9.0.9", "@types/lodash-es": "^4.17.12", "@types/node": "^22", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "eslint": "^9.20.0", "eslint-import-resolver-typescript": "^4.4.4", "eslint-plugin-boundaries": "^5.0.1", "eslint-plugin-path-alias": "^2.1.0", "eslint-plugin-prettier": "^5.4.0", "eslint-plugin-storybook": "^9.0.4", "eslint-plugin-unused-imports": "^4.1.4", "husky": "^9.1.7", "jest": "^30.0.0", "jest-environment-jsdom": "^30.0.4", "msw": "^2.7.3", "open": "^10.1.0", "prettier": "^3.5.0", "storybook": "^9.0.4", "style-dictionary": "^5.0.0", "stylelint": "^16.19.1", "stylelint-config-standard": "^38.0.0", "stylelint-no-unsupported-browser-features": "^8.0.4", "stylelint-scss": "^6.11.1", "stylelint-value-no-unknown-custom-properties": "^6.0.1", "tinycolor2": "^1.6.0", "ts-jest": "^29.3.4", "turbo": "^2.5.4", "typescript": "^5.7.3", "typescript-plugin-css-modules": "^5.1.0", "utility-types": "^3.11.0"}, "engines": {"node": ">=18"}, "packageManager": "yarn@4.9.2", "workspaces": ["apps/*", "packages/*"], "dependencies": {"@emotion/is-prop-valid": "^1.3.1", "@heroui/alert": "beta", "@heroui/avatar": "beta", "@heroui/button": "beta", "@heroui/card": "beta", "@heroui/form": "beta", "@heroui/image": "beta", "@heroui/input": "beta", "@heroui/link": "beta", "@heroui/modal": "beta", "@heroui/navbar": "beta", "@heroui/react": "beta", "@heroui/select": "beta", "@heroui/spinner": "beta", "@heroui/switch": "beta", "@heroui/system": "beta", "@heroui/theme": "beta", "@heroui/toast": "beta", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-tooltip": "^1.2.6", "@reduxjs/toolkit": "^2.6.1", "@repo/api": "*", "@repo/constants": "*", "@repo/helpers": "*", "@repo/hooks": "*", "@repo/types": "*", "@repo/ui": "*", "@tailwindcss/postcss": "^4", "@vercel/analytics": "^1.5.0", "@vercel/speed-insights": "^1.2.0", "axios": "^1.8.4", "blurhash-base64": "^0.0.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isomorphic-dompurify": "^2.25.0", "js-cookie": "^3.0.5", "json-rules-engine": "^7.3.1", "jsonwebtoken": "^9.0.2", "lodash-es": "^4.17.21", "lucide-react": "^0.523.0", "motion": "^12.11.1", "next": "^15.1.6", "next-auth": "beta", "next-cache-toolbar": "^0.3.1", "next-themes": "^0.4.6", "postcss": "^8.5.3", "query-string": "^9.1.1", "react": "^19.1.0", "react-blurhash": "^0.3.0", "react-dom": "^19.1.0", "react-hook-form": "^7.55.0", "react-redux": "^9.2.0", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.6", "tw-animate-css": "^1.2.9", "use-debounce": "^10.0.4", "usehooks-ts": "^3.1.1", "uuid": "^11.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "eslintConfig": {"extends": ["plugin:storybook/recommended"]}, "resolutions": {"stylus": "0.0.1-security"}, "comments": {"stylus": "23/07/2025 Security: Stylus package has a known vulnerability. Use the patched version 0.0.1-security."}}