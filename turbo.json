{"$schema": "https://turbo.build/schema.json", "ui": "tui", "tasks": {"build": {"dependsOn": ["^build"], "inputs": ["$TURBO_DEFAULT$", ".env*"], "outputs": [".next/**", "!.next/cache/**"]}, "lint": {"dependsOn": ["^lint"]}, "check-types": {"dependsOn": ["^check-types"]}, "test": {"dependsOn": ["^build"]}, "dev": {"cache": false, "persistent": true}, "dev:staging": {"cache": false, "persistent": true}, "dev:msw": {"cache": false, "persistent": true}, "clear-cache": {"cache": false, "persistent": true}}, "globalEnv": ["NEXT_PUBLIC_APP_NAME", "NEXT_PUBLIC_DEFAULT_LICENSE", "NEXT_PUBLIC_S3_URL", "NEXT_PUBLIC_DEFAULT_LOCALE", "NEXT_PUBLIC_TEST_USER_EMAIL", "NEXT_PUBLIC_TEST_USER_PASSWORD", "SUPPORTED_LOCALES", "NODE_ENV", "USE_MSW", "AUTH_SECRET", "RL_API_URL", "RL_API_KEY", "NEXT_PUBLIC_OMIT_DEFAULT_LOCALE_FROM_PATH", "AUTH_TRUST_HOST"], "globalDependencies": [".env", "tsconfig.json"]}