import type { Decorator } from '@storybook/nextjs'
import { LocaleProvider } from '@/context/LocaleContext'
import ReduxStoreProvider from '@/store/ReduxStoreProvider'
import { SidebarProvider } from '@repo/ui/shadcn/sidebar'

export const PROVIDERS_DECORATOR: Decorator = Story => {
  return (
    <ReduxStoreProvider>
      <LocaleProvider locale="en">
        <SidebarProvider>
          <Story />
        </SidebarProvider>
      </LocaleProvider>
    </ReduxStoreProvider>
  )
}
