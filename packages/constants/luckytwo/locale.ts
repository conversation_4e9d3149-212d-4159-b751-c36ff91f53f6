import { RhinoLanguageCodes, RhinoLicense, RhinoMarkets } from '../common/locale'

export enum Locale {
  EN = 'en',
  CA = 'ca',
  US = 'us',
}

export const LOCALES_MAP = {
  [Locale.EN]: {
    market: RhinoMarkets.RestOfWorld,
    lang: RhinoLanguageCodes.English,
  },
  [Locale.US]: {
    market: RhinoMarkets.USA,
    lang: RhinoLanguageCodes.English,
  },
  [Locale.CA]: {
    market: RhinoMarkets.Canada,
    lang: RhinoLanguageCodes.Norwegian,
  },
} as const

export const MARKET_TO_LICENSE_MAP: Record<Market, RhinoLicense> = {
  [RhinoMarkets.RestOfWorld]: RhinoLicense.GLI,
  [RhinoMarkets.Canada]: RhinoLicense.Curacao,
  [RhinoMarkets.USA]: RhinoLicense.GLI,
} as const

export type Market = (typeof LOCALES_MAP)[keyof typeof LOCALES_MAP]['market']
export type LanguageCode = (typeof LOCALES_MAP)[keyof typeof LOCALES_MAP]['lang']
export type License = (typeof MARKET_TO_LICENSE_MAP)[keyof typeof MARKET_TO_LICENSE_MAP]
