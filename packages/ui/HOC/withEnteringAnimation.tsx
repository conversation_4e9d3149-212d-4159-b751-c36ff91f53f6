import React from 'react'

export const withEnteringAnimation = <P extends object>(WrappedComponent: React.ComponentType<P>) => {
  const WithEnteringAnimation = (props: P) => {
    return (
      <div className="animate-in fade-in duration-300 ease-in-out">
        <WrappedComponent {...props} />
      </div>
    )
  }

  WithEnteringAnimation.displayName = `withEnteringAnimation(${
    WrappedComponent.displayName || WrappedComponent.name || 'Component'
  })`

  return WithEnteringAnimation
}
