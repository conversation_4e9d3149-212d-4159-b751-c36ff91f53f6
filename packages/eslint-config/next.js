import js from '@eslint/js'
import eslintConfigPrettier from 'eslint-config-prettier'
import eslint<PERSON>luginPrettier from 'eslint-plugin-prettier'
import tseslint from 'typescript-eslint'
import pluginReactHooks from 'eslint-plugin-react-hooks'
import pluginReact from 'eslint-plugin-react'
import globals from 'globals'
import pluginNext from '@next/eslint-plugin-next'
import unusedImports from 'eslint-plugin-unused-imports'
import importPlugin from 'eslint-plugin-import'
import boundaries from 'eslint-plugin-boundaries'
import typescriptParser from '@typescript-eslint/parser'
import typescriptEslintPlugin from '@typescript-eslint/eslint-plugin'

import { config as baseConfig } from './base.js'

/**
 * A custom ESLint configuration for libraries that use Next.js.
 *
 * @type {import("eslint").Linter.Config}
 * */
export const nextJsConfig = [
  ...baseConfig,
  js.configs.recommended,
  {
    plugins: {
      prettier: eslintPluginPrettier,
    },

    rules: {
      ...eslintPluginPrettier.configs.recommended.rules,
      'prettier/prettier': 'error',
    },
  },
  eslintConfigPrettier,
  importPlugin.flatConfigs.recommended,
  ...tseslint.configs.recommended,
  {
    ...pluginReact.configs.flat.recommended,
    languageOptions: {
      ...pluginReact.configs.flat.recommended.languageOptions,
      globals: {
        ...globals.serviceworker,
      },
    },
  },
  {
    plugins: {
      '@next/next': pluginNext,
    },
    rules: {
      ...pluginNext.configs.recommended.rules,
      ...pluginNext.configs['core-web-vitals'].rules,
    },
  },
  {
    plugins: {
      'react-hooks': pluginReactHooks,
    },
    settings: { react: { version: 'detect' } },
    rules: {
      ...pluginReactHooks.configs.recommended.rules,
      // React scope no longer necessary with new JSX transform.
      'react/react-in-jsx-scope': 'off',
    },
  },
  {
    rules: {
      quotes: ['error', 'single', { avoidEscape: true, allowTemplateLiterals: false }],
      'max-len': ['error', { code: 140, ignoreUrls: true, ignorePattern: '^import\\s.+from\\s.+$' }],
    },
  },
  {
    files: ['src/ui/icons/*.tsx'],
    rules: {
      'max-len': 'off',
    },
  },
  {
    files: ['**/style-dictionary.ts'],
    rules: {
      'max-len': 'off',
      'prettier/prettier': 'off',
    },
  },
  {
    languageOptions: {
      ecmaVersion: 'latest',
      sourceType: 'module',
    },
    rules: {
      'no-unused-vars': 'off',
      'import/order': [
        'error',
        {
          groups: ['builtin', 'external', 'internal', 'index', 'unknown'],
          alphabetize: {
            order: 'asc',
            caseInsensitive: true,
          },
          pathGroups: [
            {
              pattern: 'react',
              group: 'builtin',
              position: 'before',
            },
            {
              pattern: '**/*.scss',
              group: 'internal',
              position: 'after',
            },
            {
              pattern: '@*/**',
              group: 'internal',
              position: 'before',
            },
          ],
          pathGroupsExcludedImportTypes: ['builtin'],
          'newlines-between': 'never',
        },
      ],
      'import/first': 'error',
      'import/newline-after-import': 'warn',
      'import/no-duplicates': 'error',
      'import/no-unresolved': 'off',
    },
  },
  {
    plugins: {
      'unused-imports': unusedImports,
    },
    rules: {
      '@typescript-eslint/no-unused-vars': 'off', // or "@typescript-eslint/no-unused-vars": "off",
      'unused-imports/no-unused-imports': 'error',
      'unused-imports/no-unused-vars': 'off',
    },
  },
  {
    rules: {
      'react/jsx-no-leaked-render': ['error', { validStrategies: ['coerce', 'ternary'] }],
      '@typescript-eslint/consistent-type-imports': 'error',
      '@typescript-eslint/naming-convention': [
        'error',
        {
          selector: 'typeAlias',
          format: ['PascalCase'],
        },
        {
          selector: 'interface',
          format: ['PascalCase'],
          prefix: ['I'],
        },
        {
          selector: 'enum',
          format: ['PascalCase'],
        },
      ],
      'no-unneeded-ternary': 'error',
      'no-nested-ternary': 'error',
      'prefer-const': 'error',
      'react-hooks/exhaustive-deps': 'warn',
      'react/self-closing-comp': [
        'error',
        {
          component: true,
          html: true,
        },
      ],
      '@typescript-eslint/no-require-imports': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'no-warning-comments': [
        'error',
        {
          terms: ['temp'],
          location: 'start',
        },
      ],
    },
  },
  {
    languageOptions: {
      parser: typescriptParser,
    },
    plugins: {
      '@typescript-eslint': typescriptEslintPlugin,
      boundaries,
    },
    settings: {
      'import/resolver': {
        typescript: {
          alwaysTryTypes: true,
        },
      },
      'boundaries/elements': [
        {
          type: 'app',
          pattern: 'src/app/*/*/**',
          capture: ['rootRoute', 'parentRoute', 'subRoutes', 'filename'], // e.g. "rootRoute": "[locale]", parentRoute: "promotions", subRoutes: "[slug]" | "welcome-offers/[slug]", filename: "page.tsx"
        },
        {
          type: 'app-root',
          pattern: 'src/app/*/**',
          capture: ['rootRoute', 'parentRoute', 'filename'], // e.g. "rootRoute": "[locale]", parentRoute: "promotions", subRoutes: "[slug]" | "welcome-offers/[slug]", filename: "page.tsx"
        },
        {
          type: 'auth-module',
          pattern: 'src/modules/auth/**',
          capture: ['path', 'filename'],
        },
        {
          type: 'page-module',
          pattern: 'src/modules/page/**',
          capture: ['path', 'filename'],
        },
        {
          type: 'layout-modules',
          pattern: ['src/modules/sidebars/**', 'src/modules/header/**', 'src/modules/footer/**'],
          capture: ['path', 'filename'],
        },
        {
          type: 'modules',
          pattern: 'src/modules/*/**',
          capture: ['module', '_', 'filename'],
        },
        {
          type: 'dynamic-content-renderer',
          pattern: 'src/components/DynamicContentRenderer/**',
        },
        {
          type: 'screens',
          pattern: 'src/components/@screens/**',
        },
        {
          type: 'components',
          pattern: 'src/components/**/*',
        },
        {
          type: 'network',
          pattern: 'src/network/*/**',
        },
        {
          type: 'store',
          pattern: 'src/store/*/**',
        },
        {
          type: 'src',
          pattern: 'src/**', // track the entire src directory
        },
      ],
    },
    rules: {
      'boundaries/element-types': [
        2,
        {
          default: 'allow',
          rules: [
            // Disallow all module imports for all tracked elements
            {
              from: '*',
              disallow: ['modules'],
              message: 'Module imports are not allowed.',
            },
            
            // Allow direct module imports within the module
            {
              from: ['modules'],
              allow: [['modules', { module: '${from.module}' }]],
            },

            // Allow module imports of consciously exposed entities for the DynamicContentRenderer
            {
              from: ['dynamic-content-renderer'],
              disallow: ['modules'],
              message:
                "Direct imports from internal module files are not allowed. Please import from the module's index.ts instead.",
            },
            {
              from: ['dynamic-content-renderer'],
              allow: [['modules', { filename: 'index.ts' }]], // DynamicContentRenderer needs to access modules' public exports
            },

            // Temp exception while we refactor things accordingly
            {
              from: ['screens'],
              allow: ['modules'],
            },

            // Allow auth module imports of consciously exposed auth entities for all tracked elements
            {
              from: '*',
              disallow: ['auth-module'],
              message: 'Direct auth module imports are not allowed.',
            },
            {
              from: '*',
              allow: [['auth-module', { filename: 'index.ts' }]], // Allow auth module index.ts imports
            },

            // Allow consciously exposed page module imports for page.tsx files
            {
              from: ['app'],
              disallow: ['page-module'],
              message: 'Page module imports are not allowed outside page.tsx files.',
            },
            {
              from: [['app', { filename: 'page.tsx' }]],
              disallow: ['page-module'],
              message: "Direct page module imports are not allowed, import from the module's index.ts instead.",
            },
            {
              from: [['app', { filename: 'page.tsx' }]],
              allow: [['page-module', { filename: 'index.ts' }]],
            },

            // Allow consciously exposed common functionality module imports for common functionality pages e.g. promotions/page.tsx should be able to import from modules/promotions/index.ts
            {
              from: [['app', { filename: 'page.tsx' }]],
              disallow: [['modules', { module: '${from.parentRoute}' }]],
              message: 'Direct imports for common functionality modules are not allowed. Use an index.ts import instead.',
            },
            {
              from: [['app', { filename: 'page.tsx' }]],
              allow: [['modules', { module: '${from.parentRoute}', filename: 'index.ts' }]],
            },

            // Allow consciously exposed module imports for network and store
            // TODO: Consider refactoring modules to expose types separately, now network and store can also access components and other entities
            {
              from: ['network', 'store'],
              disallow: ['modules'],
              message: 'Direct imports of modules are not allowed. Use an index.ts import instead.',
            },
            {
              from: ['network', 'store'],
              allow: [['modules', { filename: 'index.ts' }]],
            },

            // Allow consciously exposed layout module imports for layout.tsx files
            {
              from: '*',
              disallow: ['layout-modules'],
              message: 'Layout module imports are not allowed outside layout.tsx files.',
            },
            {
              from: ['app-root'],
              disallow: ['layout-modules'],
              message: "Direct layout module imports are not allowed, import from the module's index.ts instead.",
            },
            {
              from: [['app-root', { filename: 'layout.tsx' }]],
              allow: [['layout-modules', { filename: 'index.ts' }]],
            },
          ],
        },
      ],
    },
  },
]
