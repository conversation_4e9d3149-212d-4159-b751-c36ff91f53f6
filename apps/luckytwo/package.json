{"name": "luckyt<PERSON>", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "npx next dev --port 3002", "dev:staging": "npx env-cmd -f .env.staging yarn dev", "dev:msw": "USE_MSW=true yarn dev", "build": "NODE_ENV=production npx next build", "start": "NODE_ENV=production npx next start --port 3002", "lint": "npx next lint", "clear-cache": "rm -rf ./.next", "check-types": "npx tsc --noEmit", "storybook": "storybook dev -p 6008 -c .storybook"}, "dependencies": {"@repo/constants": "*", "@repo/helpers": "*", "@repo/hooks": "*", "@repo/types": "*", "@repo/ui": "*"}}