/**
 * Do not edit directly, this file was auto-generated.
 */

:root {
  --a-very-core-token: #a8a8a8;
  --radius-none: 0rem;
  --radius-xxs: 0.25rem;
  --radius-xs: 0.375rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 1000rem;
  --font-family-primary: 'Funnel Sans';
  --font-family-secondary: Robot<PERSON>;
  --font-weight-extrabold: 900;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 3rem;
  --font-lineheight-tight: 1;
  --font-lineheight-normal: 1.2;
  --size-xxs: 0.25rem;
  --size-xs: 0.5rem;
  --size-sm: 0.75rem;
  --size-md: 1rem;
  --size-lg: 1.25rem;
  --size-xl: 1.5rem;
  --size-2xl: 2rem;
  --size-2xl-1: 2.25rem;
  --size-3xl-2: 2.5rem;
  --size-3xl-3: 2.75rem;
  --size-3xl: 3rem;
  --border-width-none: 0px;
  --border-width-default: 1px;
  --icon-size-xs: 1rem;
  --icon-size-sm: 1.25rem;
  --icon-size-md: 1.5rem;
  --color-surface-100: #f7faff;
  --color-surface-200: #d6e4f7;
  --color-surface-300: #cbe0f7;
  --color-surface-400: #b8d1eb;
  --color-surface-500: #97b1c9;
  --color-surface-600: #88a1ba;
  --color-surface-700: #738eaa;
  --color-surface-800: #507b96;
  --color-surface-900: #2f4e6b;
  --color-surface-1000: #1c2e38;
  --color-primary: #00b2ff;
  --color-primary-focus: #0099e1;
  --color-on-primary: #0a1a2a;
  --color-secondary: #f6f9ff;
  --color-secondary-border: #b3d6f7;
  --color-on-secondary: #1e2a38;
  --color-tertiary: linear-gradient(180deg, #1cb2c2 0%, #2fd8b0 100%);
  --color-tertiary-border: linear-gradient(180deg, #36e2c9 0%, #53ffd1 100%);
  --color-on-tertaiary: #ffffff;
  --color-error: #d13a3a;
  --color-error-container: #ffd6d6;
  --color-success: #2fd082;
  --color-on-success: #ffffff;
  --color-background: #e7f3f8;
  --color-transparent: rgba(0, 0, 0, 0);
  --color-shade-100: #fafaff;
  --color-shade-200: #d7d6e4;
  --color-shade-300: #cdcbde;
  --color-shade-400: #bab8d1;
  --color-shade-500: #9997b1;
  --color-shade-600: #8a88a1;
  --color-shade-700: #75738e;
  --color-shade-800: #52506b;
  --color-shade-900: #2f2e42;
  --color-shade-1000: #1e1c38;
  --color-yellow-400: #ffea31;
  --color-yellow-500: #ffcc24;
  --color-on-yellow: #0d0d0d;
  --typography-label-sm: var(--font-weight-extrabold) var(--font-size-sm) / var(--font-lineheight-tight)
    var(--font-family-primary);
  --typography-label-md: var(--font-weight-extrabold) var(--font-size-md) / var(--font-lineheight-tight)
    var(--font-family-primary);
  --color-scrim: rgba(var(--color-background), 0.6);
}

.dark,
[data-theme='dark'] {
  --color-primary: #24ccff;
  --color-primary-focus: #00b2e1;
  --color-on-primary: #0a1a2a;
  --color-secondary: linear-gradient(180deg, #1a2c39 0%, #1b3539 100%);
  --color-secondary-border: linear-gradient(180deg, #1b3337 0%, #1b4045 100%);
  --color-on-secondary: #ffffff;
  --color-tertiary: linear-gradient(180deg, #1cb2c2 0%, #2fd8b0 100%);
  --color-tertiary-border: linear-gradient(180deg, #36e2c9 0%, #53ffd1 100%);
  --color-on-tertaiary: #ffffff;
  --color-error: #d13a3a;
  --color-error-container: #ffd6d6;
  --color-success: #2fd082;
  --color-on-success: #ffffff;
  --color-background: #13232b;
  --color-surface-100: #181f23;
  --color-surface-200: #1d2d3f;
  --color-surface-300: #28324a;
  --color-surface-400: #323a45;
  --color-surface-500: #3b4e5e;
  --color-surface-600: #465a69;
  --color-surface-700: #687a8c;
  --color-surface-800: #93a9b7;
  --color-surface-900: #c0d0e9;
  --color-surface-1000: #f0f8ff;
  --color-yellow-400: #ffea31;
  --color-yellow-500: #ffcc24;
  --color-on-yellow: #0d0d0d;
  --color-scrim: rgba(var(--color-background), 0.6);
}
