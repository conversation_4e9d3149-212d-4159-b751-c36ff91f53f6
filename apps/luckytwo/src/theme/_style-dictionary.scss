/**
 * Do not edit directly, this file was auto-generated.
 */

$a-very-core-token: var(--a-very-core-token);
$radius-none: var(--radius-none);
$radius-xxs: var(--radius-xxs);
$radius-xs: var(--radius-xs);
$radius-sm: var(--radius-sm);
$radius-md: var(--radius-md);
$radius-lg: var(--radius-lg);
$radius-full: var(--radius-full);
$font-family-primary: var(--font-family-primary);
$font-family-secondary: var(--font-family-secondary);
$font-weight-extrabold: var(--font-weight-extrabold);
$font-size-xs: var(--font-size-xs);
$font-size-sm: var(--font-size-sm);
$font-size-md: var(--font-size-md);
$font-size-lg: var(--font-size-lg);
$font-size-xl: var(--font-size-xl);
$font-size-2xl: var(--font-size-2xl);
$font-size-3xl: var(--font-size-3xl);
$font-size-4xl: var(--font-size-4xl);
$font-lineheight-tight: var(--font-lineheight-tight);
$font-lineheight-normal: var(--font-lineheight-normal);
$size-xxs: var(--size-xxs);
$size-xs: var(--size-xs);
$size-sm: var(--size-sm);
$size-md: var(--size-md);
$size-lg: var(--size-lg);
$size-xl: var(--size-xl);
$size-2xl: var(--size-2xl);
$size-2xl-1: var(--size-2xl-1);
$size-3xl-2: var(--size-3xl-2);
$size-3xl-3: var(--size-3xl-3);
$size-3xl: var(--size-3xl);
$border-width-none: var(--border-width-none);
$border-width-default: var(--border-width-default);
$icon-size-xs: var(--icon-size-xs);
$icon-size-sm: var(--icon-size-sm);
$icon-size-md: var(--icon-size-md);
$color-surface-100: var(--color-surface-100);
$color-surface-200: var(--color-surface-200);
$color-surface-300: var(--color-surface-300);
$color-surface-400: var(--color-surface-400);
$color-surface-500: var(--color-surface-500);
$color-surface-600: var(--color-surface-600);
$color-surface-700: var(--color-surface-700);
$color-surface-800: var(--color-surface-800);
$color-surface-900: var(--color-surface-900);
$color-surface-1000: var(--color-surface-1000);
$color-primary: var(--color-primary);
$color-primary-focus: var(--color-primary-focus);
$color-on-primary: var(--color-on-primary);
$color-secondary: var(--color-secondary);
$color-secondary-border: var(--color-secondary-border);
$color-on-secondary: var(--color-on-secondary);
$color-tertiary: var(--color-tertiary);
$color-tertiary-border: var(--color-tertiary-border);
$color-on-tertaiary: var(--color-on-tertaiary);
$color-error: var(--color-error);
$color-error-container: var(--color-error-container);
$color-success: var(--color-success);
$color-on-success: var(--color-on-success);
$color-background: var(--color-background);
$color-transparent: var(--color-transparent);
$color-shade-100: var(--color-shade-100);
$color-shade-200: var(--color-shade-200);
$color-shade-300: var(--color-shade-300);
$color-shade-400: var(--color-shade-400);
$color-shade-500: var(--color-shade-500);
$color-shade-600: var(--color-shade-600);
$color-shade-700: var(--color-shade-700);
$color-shade-800: var(--color-shade-800);
$color-shade-900: var(--color-shade-900);
$color-shade-1000: var(--color-shade-1000);
$color-yellow-400: var(--color-yellow-400);
$color-yellow-500: var(--color-yellow-500);
$color-on-yellow: var(--color-on-yellow);
$typography-label-sm: var(--typography-label-sm);
$typography-label-md: var(--typography-label-md);
$color-scrim: var(--color-scrim);
