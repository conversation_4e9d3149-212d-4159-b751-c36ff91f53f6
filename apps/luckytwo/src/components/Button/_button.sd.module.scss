/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$primary-fill-color: $color-primary;
$primary-label-color: $color-on-primary;
$secondary-fill-color: $color-secondary;
$secondary-label-color: $color-on-secondary;
$secondary-border-color: $color-secondary-border;
$secondary-border-width: 1px;
$secondary-border-style: solid;
$tertiary-fill-color: $color-tertiary;
$tertiary-label-color: $color-on-tertaiary;
$tertiary-border-color: $color-tertiary-border;
$tertiary-border-width: 1px;
$tertiary-border-style: solid;
$border-radius: $border-radius-medium;
$disabled-fill-color: $color-shade-400;
$disabled-label-color: $color-shade-700;