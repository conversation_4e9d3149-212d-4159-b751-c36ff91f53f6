export * from '@core/middleware'

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico, sitemap.xml, robots.txt (metadata files)
     */
    // Skip Next.js internals and all static files, unless found in search params
    // eslint-disable-next-line max-len
    '/((?!api|_next/static|_next|_vercel|\\.well-known|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|svg|docx?|zip|webmanifest)).*)',
  ],
  unstable_allowDynamic: ['**/node_modules/lodash-es/**/*.js'],
}
