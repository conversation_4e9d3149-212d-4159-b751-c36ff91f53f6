/**
 * Do not edit directly, this file was auto-generated.
 */

:root {
  --a-very-core-token: #a8a8a8;
  --radius-none: 0rem;
  --radius-xxs: 0.25rem;
  --radius-xs: 0.375rem;
  --radius-sm: 0.5rem;
  --radius-md: 0.75rem;
  --radius-lg: 1rem;
  --radius-full: 1000rem;
  --font-family-primary: <PERSON><PERSON>;
  --font-family-secondary: 'Funnel Sans';
  --font-weight-extrabold: 900;
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-md: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 2rem;
  --font-size-4xl: 3rem;
  --font-lineheight-tight: 1;
  --font-lineheight-normal: 1.2;
  --size-xxs: 0.25rem;
  --size-xs: 0.5rem;
  --size-sm: 0.75rem;
  --size-md: 1rem;
  --size-lg: 1.25rem;
  --size-xl: 1.5rem;
  --size-2xl: 2rem;
  --size-2xl-1: 2.25rem;
  --size-3xl-2: 2.5rem;
  --size-3xl-3: 2.75rem;
  --size-3xl: 3rem;
  --border-width-none: 0px;
  --border-width-default: 1px;
  --icon-size-xs: 1rem;
  --icon-size-sm: 1.25rem;
  --icon-size-md: 1.5rem;
  --color-surface-100: #fafaff;
  --color-surface-200: #d7d6e4;
  --color-surface-300: #cdcbde;
  --color-surface-400: #bab8d1;
  --color-surface-500: #9997b1;
  --color-surface-600: #8a88a1;
  --color-surface-700: #75738e;
  --color-surface-800: #52506b;
  --color-surface-900: #2f2e42;
  --color-surface-1000: #1e1c38;
  --color-primary: #ffaa00;
  --color-primary-focus: #e1a123;
  --color-on-primary: #08082a;
  --color-secondary: #ffffff;
  --color-secondary-border: #d7d6e4;
  --color-tertiary: linear-gradient(180deg, #741cc2 0%, #b02fd8 100%);
  --color-tertiary-border: linear-gradient(180deg, #9736e2 0%, #d153ff 100%);
  --color-on-tertaiary: #ffffff;
  --color-error: #c73636;
  --color-error-container: #ff9898;
  --color-success: #82d02f;
  --color-on-success: #ffffff;
  --color-background: #e8e7f3;
  --color-transparent: rgba(0, 0, 0, 0);
  --color-shade-100: #fafaff;
  --color-shade-200: #d7d6e4;
  --color-shade-300: #cdcbde;
  --color-shade-400: #bab8d1;
  --color-shade-500: #9997b1;
  --color-shade-600: #8a88a1;
  --color-shade-700: #75738e;
  --color-shade-800: #52506b;
  --color-shade-900: #2f2e42;
  --color-shade-1000: #1e1c38;
  --color-yellow-400: #ffea31;
  --color-yellow-500: #ffcc24;
  --color-on-yellow: #0d0d0d;
  --typography-label-sm: var(--font-weight-extrabold) var(--font-size-sm) / var(--font-lineheight-tight)
    var(--font-family-primary);
  --typography-label-md: var(--font-weight-extrabold) var(--font-size-md) / var(--font-lineheight-tight)
    var(--font-family-primary);
  --color-on-secondary: var(--color-surface-1000);
  --color-scrim: rgba(var(--color-background), 0.6);
}

.dark,
[data-theme='dark'] {
  --color-primary: #ffcc24;
  --color-primary-focus: #ffc400;
  --color-on-primary: #08082a;
  --color-secondary: linear-gradient(180deg, #29292c 0%, #353539 100%);
  --color-secondary-border: linear-gradient(180deg, #333337 0%, #404045 100%);
  --color-on-secondary: #ffffff;
  --color-tertiary: linear-gradient(180deg, #741cc2 0%, #b02fd8 100%);
  --color-tertiary-border: linear-gradient(180deg, #9736e2 0%, #d153ff 100%);
  --color-on-tertaiary: #ffffff;
  --color-error: #c73636;
  --color-error-container: #ff9898;
  --color-success: #82d02f;
  --color-on-success: #ffffff;
  --color-background: #111113;
  --color-surface-100: #161618;
  --color-surface-200: #1d1d1f;
  --color-surface-300: #28282a;
  --color-surface-400: #323235;
  --color-surface-500: #3b3b3e;
  --color-surface-600: #464649;
  --color-surface-700: #68686c;
  --color-surface-800: #939397;
  --color-surface-900: #c0c0c9;
  --color-surface-1000: #ffffff;
  --color-surface-yellow-400: #ffea31;
  --color-surface-yellow-500: #ffcc24;
  --color-surface-on-yellow: #0d0d0d;
  --color-yellow-400: #ffea31;
  --color-yellow-500: #ffcc24;
  --color-on-yellow: #0d0d0d;
  --color-scrim: rgba(var(--color-background), 0.6);
}
