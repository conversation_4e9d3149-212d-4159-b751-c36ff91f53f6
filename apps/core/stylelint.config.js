const colorError = 'No ad-hoc color values, use predefined variables instead'

export default {
  ignoreFiles: ['**/*.sd.module.scss'],
  plugins: [
    'stylelint-value-no-unknown-custom-properties',
    '@repo/stylelint-plugin-autofix/declaration-property-unit-allowed-list',
    '@repo/stylelint-plugin-autofix/require-calculate-rem-import',
    '@repo/stylelint-plugin-autofix/require-scss-vars-import',
    'stylelint-no-unsupported-browser-features',
  ],
  rules: {
    'plugin/no-unsupported-browser-features': [
      true,
      {
        severity: 'warning',
        ignore: ['rem', 'flexbox', 'css-nesting'], // optionally ignore features
      },
    ],
    'color-hex-length': 'long',
    'selector-class-pattern': '^[a-z]+([A-Z][a-z]*)*$', // camelCase
    'import-notation': 'string',
    'plugin/require-calculate-rem-import': true,
    'plugin/require-scss-vars-import': true,
  },
  overrides: [
    {
      files: ['**/*.module.scss'],
      rules: {
        'color-no-hex': [true, { message: colorError }],
        'color-named': ['never', { message: colorError }],
        'declaration-property-value-disallowed-list': {
          '/.*/': ['/rgb/', '/hsl/'],
        },
        'plugin/declaration-property-unit-allowed-list': [
          {
            // Typography
            'font-size': ['rem'],

            // Spacing
            '/^padding/': ['rem'],
            '/^margin/': ['rem'],
            gap: ['rem'],

            // Sizing
            '/^width/': ['rem', '%', 'vw'],
            '/^height/': ['rem', '%', 'vh'],

            // Positioning
            top: ['rem'],
            right: ['rem'],
            bottom: ['rem'],
            left: ['rem'],

            // Borders
            border: ['px'],
            'border-top': ['px'],
            'border-right': ['px'],
            'border-bottom': ['px'],
            'border-left': ['px'],
            'border-width': ['px'],
            'border-radius': ['rem', '%'],

            // Shadows
            'box-shadow': ['px', 'rem'],

            // Line spacing
            'line-height': [''],
          },
          {
            message: property => {
              const messages = {
                'font-size': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                padding: 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'padding-top': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'padding-right': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'padding-bottom': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'padding-left': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                margin: 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'margin-top': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'margin-right': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'margin-bottom': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                'margin-left': 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                gap: 'Use px with calculate-rem() function. Example: calculate-rem(16px)',
                width: 'Use px with calculate-rem() function or %/vw. Example: calculate-rem(320px)',
                height: 'Use px with calculate-rem() function or %/vh. Example: calculate-rem(240px)',
                'max-width': 'Use px with calculate-rem() function or %/vw. Example: calculate-rem(1200px)',
                'max-height': 'Use px with calculate-rem() function or %/vh. Example: calculate-rem(800px)',
                'min-width': 'Use px with calculate-rem() function or %/vw. Example: calculate-rem(200px)',
                'min-height': 'Use px with calculate-rem() function or %/vh. Example: calculate-rem(100px)',
                top: 'Use px with calculate-rem() function. Example: calculate-rem(10px)',
                right: 'Use px with calculate-rem() function. Example: calculate-rem(10px)',
                bottom: 'Use px with calculate-rem() function. Example: calculate-rem(10px)',
                left: 'Use px with calculate-rem() function. Example: calculate-rem(10px)',
                border: 'Use px for crisp rendering of borders.',
                'border-top': 'Use px for crisp rendering of borders.',
                'border-right': 'Use px for crisp rendering of borders.',
                'border-bottom': 'Use px for crisp rendering of borders.',
                'border-left': 'Use px for crisp rendering of borders.',
                'border-width': 'Use px for crisp rendering of borders.',
                'border-radius': 'Use px with calculate-rem() function. Example: calculate-rem(8px)',
                'box-shadow': 'Use px for crisp rendering of shadows.',
                'line-height': 'Use unitless values for scaling proportionally to font size.',
              }
              return (
                messages[property] ||
                'Please follow the CSS_UNITS_GUIDE when picking the right units of measurement https://github.com/rhinoent/rhino-next/tree/master/apps/core/src/theme/CSS_UNITS_GUIDE.md'
              )
            },
          },
        ],
        'media-feature-range-notation': 'prefix', // TODO: Reconsider context
      },
    },
  ],
}
