import type { Metadata } from 'next'
import { ClientProviders } from '@/client-providers'
import { envVars } from '@/env'
import { Providers } from '@/providers'
import { PrimaryFont, SecondaryFont } from '@app/layout.settings'
import '@/app/globals.scss'
import { Analytics } from '@vercel/analytics/next'
import { SpeedInsights } from '@vercel/speed-insights/next'

const Toolbar: React.ComponentType = () => null

if (process.env.NODE_ENV === 'development') {
  // Toolbar = dynamic(() => import('@app/toolbar')) // breaks the tailwind styles
}

if (process.env.USE_MSW === 'true') {
  const msw = require('@/mock/server/server').default
  msw.startMSWServer()
}

export const metadata: Metadata = {
  title: envVars.NEXT_PUBLIC_APP_NAME,
  description: 'This is a core',
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang="en" className={`${PrimaryFont.className} ${SecondaryFont.className}`} suppressHydrationWarning>
      {/* data-scroll-locked="1" added by the sidebar adds an unnecessary right margin */}
      <body suppressHydrationWarning style={{ marginRight: '0 !important' }}>
        <Providers>
          <ClientProviders>{children}</ClientProviders>
        </Providers>
        <Analytics />
        <SpeedInsights />
        {/* <Toolbar /> */}
      </body>
    </html>
  )
}
