// This file is auto-generated from style-dictionary.css
// Do not edit directly. Update the CSS source and regenerate if needed.

export const styleDictionaryVars = {
  '--a-very-core-token': '#a8a8a8',
  '--radius-none': '0rem',
  '--radius-xxs': '0.25rem',
  '--radius-xs': '0.375rem',
  '--radius-sm': '0.5rem',
  '--radius-md': '0.75rem',
  '--radius-lg': '1rem',
  '--radius-full': '1000rem',
  '--font-family-primary': 'Roboto',
  '--font-family-secondary': 'Montserrat',
  '--font-weight-extrabold': '900',
  '--font-size-xs': '0.75rem',
  '--font-size-sm': '0.875rem',
  '--font-size-md': '1rem',
  '--font-size-lg': '1.125rem',
  '--font-size-xl': '1.25rem',
  '--font-size-2xl': '1.5rem',
  '--font-size-3xl': '2rem',
  '--font-size-4xl': '3rem',
  '--font-lineheight-tight': '1',
  '--font-lineheight-normal': '1.2',
  '--size-xxs': '0.25rem',
  '--size-xs': '0.5rem',
  '--size-sm': '0.75rem',
  '--size-md': '1rem',
  '--size-lg': '1.25rem',
  '--size-xl': '1.5rem',
  '--size-2xl': '2rem',
  '--size-2xl-1': '2.25rem',
  '--size-3xl-2': '2.5rem',
  '--size-3xl-3': '2.75rem',
  '--size-3xl': '3rem',
  '--border-width-none': '0px',
  '--border-width-default': '1px',
  '--icon-size-xs': '1rem',
  '--icon-size-sm': '1.25rem',
  '--icon-size-md': '1.5rem',
  '--color-surface-100': '#f0f0f0',
  '--color-surface-200': '#c0c0c0',
  '--color-surface-300': '#a0a0a0',
  '--color-surface-400': '#7a7a7a',
  '--color-surface-500': '#606060',
  '--color-surface-600': '#4a4a4a',
  '--color-surface-700': '#404040',
  '--color-surface-800': '#353535',
  '--color-surface-900': '#2c2c2c',
  '--color-surface-1000': '#232323',
  '--color-primary': '#a2a2a2',
  '--color-primary-focus': '#333333',
  '--color-on-primary': '#f0f0f0',
  '--color-secondary': '#f0f0f0',
  '--color-secondary-border': '#c0c0c0',
  '--color-on-secondary': '#222222',
  '--color-tertiary': '#e0e0e0',
  '--color-tertiary-border': '#c0c0c0',
  '--color-on-tertaiary': '#ffffff',
  '--color-error': '#888888',
  '--color-error-container': '#aaaaaa',
  '--color-success': '#888888',
  '--color-on-success': '#222222',
  '--color-background': '#f8f8f8',
  '--color-scrim': 'rgba(248, 248, 248, 0.6)',
  '--color-transparent': 'rgba(0, 0, 0, 0)',
  '--color-shade-100': '#fafaff',
  '--color-shade-200': '#d7d6e4',
  '--color-shade-300': '#cdcbde',
  '--color-shade-400': '#bab8d1',
  '--color-shade-500': '#9997b1',
  '--color-shade-600': '#8a88a1',
  '--color-shade-700': '#75738e',
  '--color-shade-800': '#52506b',
  '--color-shade-900': '#2f2e42',
  '--color-shade-1000': '#1e1c38',
  '--color-yellow-400': '#ffea31',
  '--color-yellow-500': '#ffcc24',
  '--color-on-yellow': '#0d0d0d',
  '--typography-label-sm': 'var(--font-weight-extrabold) var(--font-size-sm)/var(--font-lineheight-tight) var(--font-family-primary)',
  '--typography-label-md': 'var(--font-weight-extrabold) var(--font-size-md)/var(--font-lineheight-tight) var(--font-family-primary)'
}

export const styleDictionaryVarsDark = {
  ...styleDictionaryVars,
  '--color-primary': '#b0b0b0',
  '--color-primary-focus': '#a0a0a0',
  '--color-on-primary': '#222222',
  '--color-secondary': '#2c2c2c',
  '--color-secondary-border': '#404040',
  '--color-on-secondary': '#f0f0f0',
  '--color-tertiary': '#444444',
  '--color-tertiary-border': '#606060',
  '--color-on-tertaiary': '#f0f0f0',
  '--color-error': '#888888',
  '--color-error-container': '#aaaaaa',
  '--color-success': '#888888',
  '--color-on-success': '#f0f0f0',
  '--color-background': '#181818',
  '--color-scrim': 'rgba(24, 24, 24, 0.6)',
  '--color-surface-100': '#232323',
  '--color-surface-200': '#2c2c2c',
  '--color-surface-300': '#353535',
  '--color-surface-400': '#404040',
  '--color-surface-500': '#4a4a4a',
  '--color-surface-600': '#606060',
  '--color-surface-700': '#7a7a7a',
  '--color-surface-800': '#a0a0a0',
  '--color-surface-900': '#c0c0c0',
  '--color-surface-1000': '#f0f0f0',
  '--color-yellow-400': '#ffea31',
  '--color-yellow-500': '#ffcc24',
  '--color-on-yellow': '#0d0d0d'
}
