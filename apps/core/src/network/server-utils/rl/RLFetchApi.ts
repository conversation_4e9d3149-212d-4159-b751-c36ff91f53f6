import 'server-only'
import { envVars } from '@/env'
import { BaseServerFetchApi } from '@/network/server-utils/BaseServerFetchApi'
import { getRLRequestParamsByLocale } from '@/utils/locale'
import { getPromotionsTransformResponse } from '@modules/promotions'
import { rlEndpoints } from '@repo/api/endpoints/rl'
import type { IRlLoginRequestParams } from '@repo/types/api/rl/auth'
import type { IGetBannersProps, IGetBannersResponse } from '@repo/types/api/rl/banners'
import type { IBonus, IGetActiveBonusesForEcrProps, IGetBonusProps } from '@repo/types/api/rl/bonus'
import type { IRlResponseSuccess } from '@repo/types/api/rl/index'
import type { IGetPromotionsProps, IRlPromotionsResponse } from '@repo/types/api/rl/promotions'

enum RLApiAcceptVersion {
  V1 = 'application/json;v=1',
  V2 = 'application/json;v=2',
  V3 = 'application/json;v=3',
}

class RLFetchApi extends BaseServerFetchApi {
  baseUrl = envVars.RL_API_URL
  revalidate = 5
  defaultHeaders: HeadersInit = {
    Accept: RLApiAcceptVersion.V3,
    'Content-Type': 'application/json',
    'X-Api-Key': envVars.RL_API_KEY || '',
  }

  async getBanners({ locale, ...params }: IGetBannersProps) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<IGetBannersResponse>(rlEndpoints.banners, {
      headers: {
        Accept: RLApiAcceptVersion.V1,
      },
      params: {
        ...rlParams,
        ...params,
      },
    }).then(response => response?.data)
  }

  async login(params: IRlLoginRequestParams) {
    return this.fetch<Response>(rlEndpoints.login, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: RLApiAcceptVersion.V3,
      },
      body: JSON.stringify(params),
      rawResponse: true,
    })
  }

  async refreshToken(refreshToken: string) {
    return this.fetch<Response>(rlEndpoints.refreshToken, {
      method: 'GET',
      headers: {
        Accept: RLApiAcceptVersion.V1,
        Authorization: `Bearer ${refreshToken}`,
      },
      rawResponse: true,
    })
  }

  async getPromotions({ isLoggedIn, locale, ...params }: IGetPromotionsProps) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<IRlResponseSuccess<IRlPromotionsResponse>>(rlEndpoints.promotions, {
      params: {
        ...params,
        ...rlParams,
      },
    }).then(response => (response ? getPromotionsTransformResponse(response, { isLoggedIn }) : null))
  }

  async getBonus({ id, locale, ...params }: IGetBonusProps) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<IRlResponseSuccess<IBonus>>(rlEndpoints.bonus, {
      params: {
        ...params,
        ...rlParams,
        id,
      },
    }).then(response => response?.data)
  }

  async getActiveBonusesForEcr({ ecrId, locale, ...params }: IGetActiveBonusesForEcrProps) {
    const rlParams = getRLRequestParamsByLocale(locale)
    return this.fetch<IRlResponseSuccess<IBonus[]>>(rlEndpoints.activeBonusesForEcr, {
      params: {
        ...params,
        ...rlParams,
        ecrId,
      },
    }).then(response => response?.data)
  }
}

export const rlFetchApi = new RLFetchApi()
