'use client'
import React from 'react'
import { ThemeToggle } from '@components/ThemeToggle'
import { NavbarContent } from '@heroui/navbar'
import { useAuthStore } from '@modules/auth'
import { HeaderNavigation } from '@modules/header/components'
import { LogoutButton } from '@modules/header/logged-in/LogoutButton'

export const LoggedInHeader = () => {
  const status = useAuthStore(state => state.status)
  const userInfo = useAuthStore(state => state.userInfo)

  return (
    <>
      <HeaderNavigation />
      <NavbarContent justify="end">
        <ThemeToggle />
        {status === 'authenticated' && <LogoutButton />}
        <p>{userInfo?.playerId}</p>
      </NavbarContent>
    </>
  )
}
