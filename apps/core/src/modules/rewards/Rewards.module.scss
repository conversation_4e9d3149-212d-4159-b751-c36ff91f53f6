@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

.container {
  width: 100%;
  margin: calculate-rem(16px) 0;
}

.title {
  font-size: calculate-rem(16px);
  font-weight: 800;
  color: $color-on-secondary;
  margin-bottom: calculate-rem(10px);

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(14px);
    margin-bottom: calculate-rem(8px);
  }
}

.scrollContainer {
  width: 100%;
  display: flex;
  gap: calculate-rem(12px);
  padding: calculate-rem(10px) 0;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  @include custom-scrollbar();

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(12px);
  }
}
