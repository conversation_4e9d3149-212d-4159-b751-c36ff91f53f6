import type { <PERSON>a, StoryObj } from '@storybook/nextjs'
import { MyRewards } from '@modules/rewards'
import type { DynamicallyRenderedMyRewardsConfigType } from '@modules/rewards/MyRewards.schema'

const meta: Meta<typeof MyRewards> = {
  title: 'Modules/Rewards/MyRewards',
  component: MyRewards,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'The MyRewards module displays a horizontal scrolling list of reward cards. ' +
          'It is used as part of the dynamic content system and renders multiple RewardCard components ' +
          'with simple horizontal scrolling functionality.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    config: {
      title: 'My Rewards',
      items: [
        {
          component: 'RewardCard',
          meta: {
            title: '100 FS at Hercules Son of Zeus',
            subTitle: 'Free spins',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim now',
            ctaHref: '#',
          },
        },
        {
          component: 'RewardCard',
          meta: {
            title: '50 FS at Book of Dead',
            subTitle: 'Free spins',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim now',
            ctaHref: '#',
          },
        },
        {
          component: 'RewardCard',
          meta: {
            title: '25 FS at Starburst',
            subTitle: 'Free spins',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim now',
            ctaHref: '#',
          },
        },
        {
          component: 'RewardCard',
          meta: {
            title: '200 FS at Gonzo\'s Quest',
            subTitle: 'Free spins',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim now',
            ctaHref: '#',
          },
        },
        {
          component: 'RewardCard',
          meta: {
            title: '75 FS at Mega Moolah',
            subTitle: 'Free spins',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim now',
            ctaHref: '#',
          },
        },
      ],
    } as DynamicallyRenderedMyRewardsConfigType,
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Configuration object containing title and array of reward items',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '100%',
          maxWidth: '1200px',
          padding: '20px',
          background: '#f8f9fa',
          borderRadius: '8px',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const Variants: Story = {
  argTypes: {
    config: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'Different variations of the MyRewards module showing various configurations and states.',
      },
    },
  },
  render: () => {
    const singleReward: DynamicallyRenderedMyRewardsConfigType = {
      title: 'Single Reward',
      items: [
        {
          component: 'RewardCard',
          meta: {
            title: 'Welcome Bonus',
            subTitle: 'New player reward',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim Welcome Bonus',
            ctaHref: '#',
          },
        },
      ],
    }

    const manyRewards: DynamicallyRenderedMyRewardsConfigType = {
      title: 'Many Rewards (Scrollable)',
      items: Array.from({ length: 8 }, (_, i) => ({
        component: 'RewardCard' as const,
        meta: {
          title: `Reward ${i + 1}`,
          subTitle: 'Free spins',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim now',
          ctaHref: '#',
        },
      })),
    }

    const noTitle: DynamicallyRenderedMyRewardsConfigType = {
      items: [
        {
          component: 'RewardCard',
          meta: {
            title: 'No Title Module',
            subTitle: 'This module has no title',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim',
            ctaHref: '#',
          },
        },
        {
          component: 'RewardCard',
          meta: {
            title: 'Another Reward',
            subTitle: 'Without module title',
            backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
            thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
            ctaLabel: 'Claim',
            ctaHref: '#',
          },
        },
      ],
    }

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Configuration Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Single Reward</h4>
              <MyRewards config={singleReward} />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Many Rewards (Horizontal Scroll)</h4>
              <MyRewards config={manyRewards} />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Module Title</h4>
              <MyRewards config={noTitle} />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive MyRewards module with customizable configuration.',
      },
    },
  },
  render: args => (
    <div
      style={{
        width: '100%',
        maxWidth: '1200px',
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
      }}>
      <MyRewards {...args} />
    </div>
  ),
}
