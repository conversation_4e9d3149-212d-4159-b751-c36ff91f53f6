@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  width: 100%;
  margin-bottom: calculate-rem(24px);
}

.title {
  font-size: calculate-rem(24px);
  font-weight: 700;
  color: $color-on-secondary;
  margin-bottom: calculate-rem(16px);
  margin: 0 0 calculate-rem(16px) 0;

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(20px);
    margin-bottom: calculate-rem(12px);
  }
}

.scrollContainer {
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;

  // Hide scrollbar but keep functionality
  scrollbar-width: none; // Firefox
  -ms-overflow-style: none; // IE and Edge

  &::-webkit-scrollbar {
    display: none; // Chrome, Safari, Opera
  }
}

.rewardsGrid {
  display: flex;
  gap: calculate-rem(16px);
  padding: calculate-rem(4px) 0; // Small padding to prevent cut-off
  min-width: fit-content;

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(12px);
  }
}

.rewardItem {
  flex: 0 0 auto; // Don't grow or shrink, maintain natural size
  width: calculate-rem(259px); // Fixed width based on RewardCard design

  @media (max-width: $breakpoint-mobile) {
    width: calculate-rem(220px); // Slightly smaller on mobile
  }
}
