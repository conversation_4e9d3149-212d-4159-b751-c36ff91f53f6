'use client'
import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { RewardCard } from '@modules/rewards/components'
import type { DynamicallyRenderedRewardsConfigType } from '@modules/rewards/Rewards.schema'
import styles from '@modules/rewards/Rewards.module.scss'

export interface IRewardsProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedRewardsConfigType
}

const Rewards: FC<IRewardsProps> = ({ config }) => {
  if (!config || !config.items || config.items.length === 0) {
    return null
  }

  const { title = 'My Rewards', items: rewards } = config

  return (
    <div className={styles.container}>
      {!!title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        {rewards.map((reward, index) => (
          <div key={`${reward.title}-${index}`}>
            <RewardCard {...reward} />
          </div>
        ))}
      </div>
    </div>
  )
}

export default Rewards
