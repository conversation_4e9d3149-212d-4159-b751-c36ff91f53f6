@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;
@use '@theme/mixins.scss' as *;

//TODO: use color tokens

$color-border: #9736e2;
$color-background: #d16eff47;

.rewardCard {
  width: calculate-rem(259px);
  height: calculate-rem(280px);
  position: relative;
  border-radius: calculate-rem(8px);
  display: flex;
  flex-direction: column;
  gap: calculate-rem(24px);
  align-items: center;
  justify-content: space-between;
  padding: calculate-rem(24px) calculate-rem(12px) calculate-rem(12px) calculate-rem(12px);
  text-align: center;

  @media (max-width: $breakpoint-mobile) {
    width: calculate-rem(213px);
    height: calculate-rem(236px);
  }
}

.background {
  z-index: 0;
  pointer-events: none;
  border-radius: inherit;
}

.thumbnail {
  display: flex;
  width: 82px;
  height: 82px;
  flex-shrink: 0;
  z-index: 0;
  border-radius: calculate-rem(12px);
  border: calculate-rem(2px) solid $color-border;
  background: $color-background;
  backdrop-filter: blur(7.4px);
  padding: calculate-rem(7px);

  img {
    pointer-events: none;
    border-radius: inherit;
    background-size: cover;
  }
}

.content {
  width: 100%;
  height: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: calculate-rem(8px);
  z-index: 1;
  color: $color-on-tertaiary;
}

.titleContainer {
  max-width: calculate-rem(156px);
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: calculate-rem(8px);

  @media (max-width: $breakpoint-mobile) {
    gap: calculate-rem(2px);
    margin-bottom: calculate-rem(4px);
    max-width: calculate-rem(133px);
  }
}

.title {
  color: $color-on-tertaiary;
  font-size: calculate-rem(18px);
  font-weight: 800;
  line-height: calculate-rem(22px);
  letter-spacing: calculate-rem(0.18px);
  @include line-clamp(3);

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(14px);
  }
}

.subTitle {
  color: $color-surface-700;
  font-size: calculate-rem(14px);
  font-weight: 500;
  @include line-clamp(1);

  @media (max-width: $breakpoint-mobile) {
    font-size: calculate-rem(12px);
  }
}

.ctaButton {
  width: 100%;
  display: flex;
  justify-content: center;
}
