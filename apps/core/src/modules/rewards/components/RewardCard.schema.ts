import { z } from 'zod'

export const RewardCardConfigSchema = z.object({
  component: z.literal('RewardCard'),
  meta: z.object({
    title: z.string(),
    subTitle: z.string(),
    backgroundImageSrc: z.string().url().optional(),
    thumbnailSrc: z.string().url().optional(),
    ctaLabel: z.string().optional(),
    ctaHref: z.string().optional(),
    ctaActionKey: z.string().optional(),
  }),
})

export type RewardCardConfigType = z.infer<typeof RewardCardConfigSchema>['meta']
