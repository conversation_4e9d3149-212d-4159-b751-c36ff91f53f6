import { RewardCard } from '@modules/rewards/components'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof RewardCard> = {
  title: 'Modules/Rewards/RewardCard',
  component: RewardCard,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A reward card component displaying a game thumbnail, title, subtitle, and CTA button, with optional background image. ' +
          'This component is part of the rewards module and is used within the MyRewards component.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    title: '100 FS at Hercules Son of Zeus',
    subTitle: 'Free spins',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim now',
    ctaHref: '#',
  },
  argTypes: {
    title: {
      control: 'text',
      description: 'The main title of the reward',
    },
    subTitle: {
      control: 'text',
      description: 'The subtitle describing the reward type',
    },
    backgroundImageSrc: {
      control: 'text',
      description: 'Optional background image URL for the card',
    },
    thumbnailSrc: {
      control: 'text',
      description: 'Optional thumbnail image URL',
    },
    ctaLabel: {
      control: 'text',
      description: 'Optional call-to-action button label',
    },
    ctaHref: {
      control: 'text',
      description: 'Optional URL for the CTA button',
    },
    ctaActionKey: {
      control: 'text',
      description: 'Optional action key for analytics or tracking',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '280px',
          padding: '20px',
          borderRadius: '8px',
          position: 'relative',
          background: '#f8f9fa',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {}

export const Variants: Story = {
  argTypes: {
    title: { table: { disable: true } },
    subTitle: { table: { disable: true } },
    backgroundImageSrc: { table: { disable: true } },
    thumbnailSrc: { table: { disable: true } },
    ctaLabel: { table: { disable: true } },
    ctaHref: { table: { disable: true } },
    ctaActionKey: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'Different variations of the RewardCard component showing various content and states.',
      },
    },
  },
  render: () => {
    const defaultBackground = 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png'
    const defaultThumbnail = 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png'

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Content Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Default</h4>
              <RewardCard
                title="100 FS at Hercules Son of Zeus"
                subTitle="Free spins"
                backgroundImageSrc={defaultBackground}
                thumbnailSrc={defaultThumbnail}
                ctaLabel="Claim now"
                ctaHref="#"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Long Title</h4>
              <RewardCard
                title="200 Free Spins at Book of Dead with Extra Bonus Features"
                subTitle="Premium reward"
                backgroundImageSrc={defaultBackground}
                thumbnailSrc={defaultThumbnail}
                ctaLabel="Activate Premium"
                ctaHref="#"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No CTA Button</h4>
              <RewardCard
                title="50 FS at Starburst"
                subTitle="Free spins"
                backgroundImageSrc={defaultBackground}
                thumbnailSrc={defaultThumbnail}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Background Image</h4>
              <RewardCard
                title="25 FS at Gonzo's Quest"
                subTitle="Adventure spins"
                thumbnailSrc={defaultThumbnail}
                ctaLabel="Start Adventure"
                ctaHref="#"
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Thumbnail</h4>
              <RewardCard
                title="Mega Moolah Jackpot"
                subTitle="Progressive jackpot"
                backgroundImageSrc={defaultBackground}
                ctaLabel="Play Now"
                ctaHref="#"
              />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive reward card with all properties available for customization.',
      },
    },
  },
  args: {
    title: 'Interactive Reward',
    subTitle: 'Customize me!',
    backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
    thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
    ctaLabel: 'Claim Reward',
    ctaHref: '#playground',
  },
  render: args => (
    <div
      style={{
        width: '280px',
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <RewardCard {...args} />
    </div>
  ),
}
