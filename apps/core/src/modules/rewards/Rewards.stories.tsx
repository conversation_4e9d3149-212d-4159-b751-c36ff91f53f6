import { Locale } from '@constants/locale'
import { Rewards } from '@modules/rewards'
import type { DynamicallyRenderedRewardsConfigType } from '@modules/rewards/Rewards.schema'
import type { Meta, StoryObj } from '@storybook/nextjs'

const meta: Meta<typeof Rewards> = {
  title: 'Modules/Rewards/Rewards',
  component: Rewards,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'The Rewards module displays a horizontal scrolling list of reward cards. ' +
          'It is used as part of the dynamic content system and renders multiple RewardCard components ' +
          'with simple horizontal scrolling functionality.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    config: {
      title: 'My Rewards',
      items: [
        {
          title: '100 FS at Hercules Son of Zeus',
          subTitle: 'Free spins',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim now',
          ctaHref: '#',
        },
        {
          title: '50 FS at Book of Dead',
          subTitle: 'Free spins',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim now',
          ctaHref: '#',
        },
        {
          title: '25 FS at Starburst',
          subTitle: 'Free spins',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim now',
          ctaHref: '#',
        },
        {
          title: "200 FS at Gonzo's Quest",
          subTitle: 'Free spins',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim now',
          ctaHref: '#',
        },
        {
          title: '75 FS at Mega Moolah',
          subTitle: 'Free spins',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim now',
          ctaHref: '#',
        },
      ],
    } as DynamicallyRenderedRewardsConfigType,
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Configuration object containing title and array of reward items',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '100%',
          maxWidth: '1200px',
          padding: '20px',
          background: '#f8f9fa',
          borderRadius: '8px',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    config: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'Different variations of the Rewards module showing various configurations and states.',
      },
    },
  },
  render: () => {
    const singleReward: DynamicallyRenderedRewardsConfigType = {
      title: 'Single Reward',
      items: [
        {
          title: 'Welcome Bonus',
          subTitle: 'New player reward',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim Welcome Bonus',
          ctaHref: '#',
        },
      ],
    }

    const manyRewards: DynamicallyRenderedRewardsConfigType = {
      title: 'Many Rewards (Scrollable)',
      items: Array.from({ length: 8 }, (_, i) => ({
        title: `Reward ${i + 1}`,
        subTitle: 'Free spins',
        backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
        thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
        ctaLabel: 'Claim now',
        ctaHref: '#',
      })),
    }

    const noTitle: DynamicallyRenderedRewardsConfigType = {
      items: [
        {
          title: 'No Title Module',
          subTitle: 'This module has no title',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim',
          ctaHref: '#',
        },
        {
          title: 'Another Reward',
          subTitle: 'Without module title',
          backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
          thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
          ctaLabel: 'Claim',
          ctaHref: '#',
        },
      ],
    }

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Configuration Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <Rewards config={singleReward} locale={Locale.EN} />
            </div>

            <div>
              <Rewards config={manyRewards} locale={Locale.EN} />
            </div>

            <div>
              <Rewards config={noTitle} locale={Locale.EN} />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive Rewards module with customizable configuration.',
      },
    },
  },
  render: args => (
    <div
      style={{
        width: '100%',
        maxWidth: '1200px',
        background: '#f8f9fa',
        padding: '20px',
        borderRadius: '8px',
      }}>
      <Rewards {...args} />
    </div>
  ),
}
