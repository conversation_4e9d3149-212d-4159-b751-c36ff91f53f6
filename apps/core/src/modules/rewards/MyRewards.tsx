'use client'
import type { FC } from 'react'
import React from 'react'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { RewardCardWidget } from '@widgets/RewardCardWidget'
import type { DynamicallyRenderedMyRewardsConfigType } from '@modules/rewards/MyRewards.schema'
import styles from '@modules/rewards/MyRewards.module.scss'

export interface IMyRewardsProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedMyRewardsConfigType
}

const MyRewards: FC<IMyRewardsProps> = ({ config, locale }) => {
  if (!config || !config.items || config.items.length === 0) {
    return null
  }

  const { title = 'My Rewards', items: rewards } = config

  return (
    <div className={styles.container}>
      {title && <h2 className={styles.title}>{title}</h2>}
      <div className={styles.scrollContainer}>
        <div className={styles.rewardsGrid}>
          {rewards.map((reward, index) => (
            <div key={index} className={styles.rewardItem}>
              <RewardCardWidget config={reward.meta} locale={locale} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default MyRewards
