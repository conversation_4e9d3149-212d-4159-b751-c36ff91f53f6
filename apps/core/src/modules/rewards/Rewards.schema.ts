import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { RewardCardConfigSchema } from '@modules/rewards/components/RewardCard.schema'

export const DynamicallyRenderedRewardsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.REWARDS),
  meta: z.object({
    title: z.string().optional(),
    items: z.array(RewardCardConfigSchema),
  }),
})

export type DynamicallyRenderedRewardsConfigType = z.infer<typeof DynamicallyRenderedRewardsConfigSchema>['meta']
