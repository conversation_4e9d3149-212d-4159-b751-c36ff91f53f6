import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicallyRenderedRewardCardConfigSchema } from '@widgets/RewardCardWidget/RewardCardWidget.schema'

export const DynamicallyRenderedMyRewardsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.MY_REWARDS),
  meta: z.object({
    title: z.string().optional(),
    items: z.array(DynamicallyRenderedRewardCardConfigSchema),
  }),
})

export type DynamicallyRenderedMyRewardsConfigType = z.infer<typeof DynamicallyRenderedMyRewardsConfigSchema>['meta']
