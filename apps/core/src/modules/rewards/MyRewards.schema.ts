import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

// Schema for individual reward card data (not a widget)
export const RewardCardConfigSchema = z.object({
  component: z.literal('RewardCard'),
  meta: z.object({
    title: z.string(),
    subTitle: z.string(),
    backgroundImageSrc: z.string().url().optional(),
    thumbnailSrc: z.string().url().optional(),
    ctaLabel: z.string().optional(),
    ctaHref: z.string().optional(),
    ctaActionKey: z.string().optional(),
  }),
})

export const DynamicallyRenderedMyRewardsConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.MY_REWARDS),
  meta: z.object({
    title: z.string().optional(),
    items: z.array(RewardCardConfigSchema),
  }),
})

export type DynamicallyRenderedMyRewardsConfigType = z.infer<typeof DynamicallyRenderedMyRewardsConfigSchema>['meta']
