import type { FC } from 'react'
import React from 'react'
import { withSuspense } from '@/HOC/withSuspense'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import SlotsContent from '@modules/slots/Slots.content'
import { Skeleton } from '@repo/ui/shadcn/skeleton'
import styles from '@modules/slots/Slots.module.scss'

const Slots: FC<IDynamicallyRenderedContentProps> = async ({ locale }) => {
  return (
    <div className={styles.container}>
      <h2>Slots</h2>
      {withSuspense(
        <SlotsContent locale={locale} />,
        <>
          <Skeleton className="h-[200px] mb-1" />
          <Skeleton className="h-[200px] mb-1" />
        </>,
      )}
    </div>
  )
}

export default Slots
