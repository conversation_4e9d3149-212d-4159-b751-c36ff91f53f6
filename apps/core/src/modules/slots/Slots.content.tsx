import type { FC } from 'react'
import React from 'react'
import { getCasinoConfig } from '@/network/server-utils/s3/getters'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { GamesSection } from '@components/GamesSection'

const SlotsContent: FC<IDynamicallyRenderedContentProps> = async ({ locale }) => {
  const casinoConfig = await getCasinoConfig(locale)

  return (
    <>
      {casinoConfig?.sections?.map((section, index) => (
        <GamesSection key={section.id} section={section} locale={locale} priority={index <= 2} />
      ))}
    </>
  )
}

export default SlotsContent
