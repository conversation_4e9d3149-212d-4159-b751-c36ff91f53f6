import React from 'react'
import { Card, CardBody, CardHeader } from '@heroui/card'
import { RegistrationForm } from '@modules/registration/components/RegistrationForm'
import styles from '@modules/registration/RegistrationScreen.module.scss'

export const RegistrationScreen = async () => {
  return (
    <Card classNames={{ base: styles.card }}>
      <CardHeader className={styles.header}>
        <div className={styles.iconContainer}>
          <span className={styles.icon}>🎁</span>
          <span className={styles.welcomeText}>Welcome to</span>
        </div>
        <h1 className={styles.title}>Registration</h1>
      </CardHeader>
      <CardBody>
        <RegistrationForm />
      </CardBody>
    </Card>
  )
}
