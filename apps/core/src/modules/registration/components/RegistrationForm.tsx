'use client'
import React from 'react'
import { clsx } from 'clsx'
import { Controller } from 'react-hook-form'
import { Alert } from '@heroui/alert'
import { Button } from '@heroui/button'
import { Checkbox } from '@heroui/checkbox'
import { Form } from '@heroui/form'
import { Input } from '@heroui/input'
import { useIsAuthenticated } from '@modules/auth'
import { useRegistrationForm } from '@modules/registration/hooks/useRegistrationForm'
import styles from '@modules/registration/components/RegistrationForm.module.scss'

interface IRegistrationFormProps {
  onSuccess?: VoidFunction
}

export const RegistrationForm: React.FC<IRegistrationFormProps> = ({ onSuccess }) => {
  const { form, registerError, pending, handleSubmit } = useRegistrationForm({ onSuccess })
  const isAuthenticated = useIsAuthenticated()

  const { watch, setValue } = form
  const hasPromoCode = watch('hasPromoCode')

  return (
    <Form onSubmit={form.handleSubmit(handleSubmit)} className={clsx(styles.form)} validationBehavior="aria">
      <div className={styles.formContent}>
        <div className={styles.tabs}>
          <Button variant="solid" color="secondary" className={styles.activeTab}>
            Sign up
          </Button>
          <Button variant="ghost" className={styles.inactiveTab}>
            Sign in
          </Button>
        </div>

        <Controller
          name="email"
          control={form.control}
          render={({ field }) => (
            <Input
              label="Enter your e-mail"
              type="email"
              autoComplete="email"
              variant="bordered"
              isClearable
              classNames={{
                input: styles.input,
                inputWrapper: styles.inputWrapper,
              }}
              {...field}
              onClear={() => {
                field.onChange('')
              }}
              errorMessage={form.formState.errors.email?.message}
              isInvalid={!!form.formState.errors.email?.message}
            />
          )}
        />

        <Controller
          name="password"
          control={form.control}
          render={({ field }) => (
            <Input
              label="Create password"
              type="password"
              autoComplete="new-password"
              variant="bordered"
              isClearable
              classNames={{
                input: styles.input,
                inputWrapper: styles.inputWrapper,
              }}
              {...field}
              onClear={() => {
                field.onChange('')
              }}
              errorMessage={form.formState.errors.password?.message}
              isInvalid={!!form.formState.errors.password?.message}
            />
          )}
        />

        {/* Promo code section */}
        <Controller
          name="hasPromoCode"
          control={form.control}
          render={({ field }) => (
            <Checkbox
              isSelected={field.value}
              onValueChange={checked => {
                field.onChange(checked)
                if (!checked) {
                  setValue('promoCode', '')
                }
              }}
              className={''}>
              <span className={styles.checkboxIcon}>🎁</span>
              <span className={styles.checkboxText}>Enter Referral / Promo Code</span>
              <span className={styles.dropdownIcon}>↓</span>
            </Checkbox>
          )}
        />

        {/* Promo code input - shown when checkbox is checked */}
        {hasPromoCode ? (
          <Controller
            name="promoCode"
            control={form.control}
            render={({ field }) => (
              <Input
                placeholder="Enter promo code"
                variant="bordered"
                classNames={{
                  input: styles.input,
                  inputWrapper: styles.inputWrapper,
                }}
                {...field}
              />
            )}
          />
        ) : null}

        {/* Terms and conditions */}
        <Controller
          name="agreeToTerms"
          control={form.control}
          render={({ field }) => (
            <Checkbox
              isSelected={field.value}
              onValueChange={field.onChange}
              isInvalid={!!form.formState.errors.agreeToTerms?.message}
              className={styles.termsCheckbox}>
              <span className={styles.termsText}>
                I agree to the <span className={styles.link}>User Agreement</span> & confirm I am at least 18 years old
              </span>
            </Checkbox>
          )}
        />

        {/* Marketing emails */}
        <Controller
          name="agreeToMarketing"
          control={form.control}
          render={({ field }) => (
            <Checkbox isSelected={field.value} onValueChange={field.onChange} className={styles.marketingCheckbox}>
              <span className={styles.marketingText}>I agree to receive marketing emails to my email address</span>
            </Checkbox>
          )}
        />

        {/* Error message */}
        {!!registerError && (
          <div className={styles.errorContainer}>
            <Alert color="danger" variant="solid" description={registerError} title="Registration Error" />
          </div>
        )}

        {/* Register button */}
        <Button
          type="submit"
          isLoading={pending}
          disabled={isAuthenticated}
          className={styles.registerButton}
          size="lg">
          Register now
        </Button>

        {/* Divider */}
        <div className={styles.divider}>
          <span className={styles.dividerText}>Log in directly with</span>
        </div>

        {/* Social login buttons */}
        <div className={styles.socialButtons}>
          <Button variant="bordered" className={styles.socialButton}>
            <span className={styles.socialIcon}>f</span>
          </Button>
          <Button variant="bordered" className={styles.socialButton}>
            <span className={styles.socialIcon}>G</span>
          </Button>
        </div>
      </div>
    </Form>
  )
}
