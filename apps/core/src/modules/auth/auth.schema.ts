import { z } from 'zod'

export const loginSchema = z.object({
  email: z
    .string()
    .trim()
    .min(1, { message: 'Please enter the email' })
    .email({ message: 'Please enter a valid email' }),
  password: z.string().trim().min(1, { message: 'Please enter the password' }),
})

export const registerSchema = z.object({
  name: z.string().min(1),
  email: z.string().min(1).email(),
  password: z.string().min(8),
})

export type ILoginSchema = z.infer<typeof loginSchema>
