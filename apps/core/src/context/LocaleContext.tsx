'use client'
import { createContext, useContext } from 'react'
import type { Locale } from '@constants/locale'

const LocaleContext = createContext<Locale | undefined>(undefined)

export const LocaleProvider = ({ locale, children }: { locale: Locale; children: React.ReactNode }) => {
  return <LocaleContext.Provider value={locale}>{children}</LocaleContext.Provider>
}

export const useLocale = () => {
  const context = useContext(LocaleContext)
  if (context === undefined) {
    throw new Error('useLocale must be used within a LocaleProvider')
  }
  return context
}
