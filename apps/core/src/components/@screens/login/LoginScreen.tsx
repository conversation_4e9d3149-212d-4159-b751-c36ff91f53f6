import React from 'react'
import { Card, CardBody, CardHeader } from '@heroui/card'
import { LoginForm } from '@screens/login/components/LoginForm'
import styles from '@screens/login/LoginScreen.module.scss'

export const LoginScreen = async () => {
  return (
    <Card classNames={{ base: styles.card }}>
      <CardHeader>
        <h2>Login</h2>
      </CardHeader>
      <CardBody>
        <LoginForm />
      </CardBody>
    </Card>
  )
}
