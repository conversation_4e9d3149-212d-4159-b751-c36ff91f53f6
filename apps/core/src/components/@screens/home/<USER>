@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.page {
  display: grid;
  grid-template-rows: 20px 1fr 20px;
  place-items: center;
  min-height: 100svh;
  padding: calculate-rem(80px);
  gap: calculate-rem(64px);
  font-synthesis: none;
}

.main {
  display: flex;
  flex-direction: column;
  gap: calculate-rem(32px);
  grid-row-start: 2;
}

.footer {
  grid-row-start: 3;
  display: flex;
  gap: calculate-rem(24px);
}

.footer a {
  display: flex;
  align-items: center;
  gap: calculate-rem(8px);
}

.footer img {
  flex-shrink: 0;
}

/* Enable hover only on non-touch devices */
@media (hover: hover) and (pointer: fine) {
  a.primary:hover {
    background: $color-background;
    border-color: transparent;
  }

  a.secondary:hover {
    background: $color-primary;
    border-color: transparent;
  }

  .footer a:hover {
    text-decoration: underline;
    text-underline-offset: 4px;
  }
}
