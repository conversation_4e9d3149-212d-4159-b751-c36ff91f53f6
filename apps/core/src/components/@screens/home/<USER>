import { redirect } from 'next/navigation'
import { authService } from '@/services/auth.service'
import { getAppRouter } from '@/services/router.service'
import type { RouteParams } from '@/types/nextjs'
import { Landing } from '@screens/home/<USER>/Landing/Landing'

export async function Home({ locale }: RouteParams) {
  const loggedIn = await authService.isAuthenticated()

  if (loggedIn) {
    redirect(getAppRouter(locale).dashboard)
  }

  return <Landing locale={locale} />
}
