import React from 'react'
import { getWelcomePageConfig } from '@/network/server-utils/s3/getters'
import { Banner } from '@components/Banner'
import type { Locale } from '@constants/locale'
import { LandingGamesSection } from '@screens/home/<USER>/Landing/LandingGamesSection/LandingGamesSection'
import styles from '@screens/home/<USER>/Landing/Landing.module.scss'

interface ILandingProps {
  locale: Locale
}

export const Landing = async ({ locale }: ILandingProps) => {
  const welcomePageConfig = await getWelcomePageConfig(locale)

  return (
    <>
      <Banner locale={locale} />
      <div className={styles.content}>
        <LandingGamesSection />
        <LandingGamesSection />
      </div>
    </>
  )
}
