import React from 'react'
import { getAvailableGamesMap } from '@/network/server-utils/s3/getters/games'
import { authService } from '@/services/auth.service'
import type { RouteParams } from '@/types/nextjs'
import { GameTile } from '@components/GameTile'
import type { GetCasinoConfigResponse } from '@repo/types/api/s3/casino-config'
import { withEnteringAnimation } from '@repo/ui/HOC/withEnteringAnimation'
import styles from '@components/GamesSection/GamesSection.module.scss'

type GamesSectionProps = RouteParams & {
  section: GetCasinoConfigResponse['sections'][0]
  priority?: boolean
}

export const GamesSection = async ({ section, locale, priority }: GamesSectionProps) => {
  const sectionGames = section.games || []
  const gamesMap = await getAvailableGamesMap(locale)
  const isLoggedIn = await authService.isAuthenticated()

  if (!sectionGames.length) {
    console.warn(`No games found for section: ${section.name}`)
    return null
  }

  const desktopGamesLimit = section.desktopGamesLimit || 30
  const desktopRowCount = section.desktopRowCount || 1
  const mobileGamesLimit = section.mobileGamesLimit || 30
  const mobileRowCount = section.mobileRowCount || 1
  const totalGamesToShow = Math.max(desktopGamesLimit, mobileGamesLimit)
  const gamesFitPerRow = 6

  const gamesToRender = sectionGames
    .map(gameId => gamesMap[gameId.toString()])
    .filter((game): game is NonNullable<typeof game> => Boolean(game))
    .slice(0, totalGamesToShow)

  const totalAvailableGames = gamesToRender.length

  if (!totalAvailableGames) {
    console.warn(`No available games found for section: ${section.name}`)
    return null
  }

  const gamesContainerStyle = {
    '--desktop-row-count': totalAvailableGames < gamesFitPerRow * desktopRowCount ? 1 : desktopRowCount,
    '--mobile-row-count': totalAvailableGames < gamesFitPerRow * mobileRowCount ? 1 : mobileRowCount,
  } as React.CSSProperties

  return (
    <div className={styles.container}>
      <h3>{section.name}</h3>
      <div className={styles.games} style={gamesContainerStyle}>
        {gamesToRender.map((game, index) => (
          <GameTile
            key={`${game.id}-${index}-${section.id}`}
            game={game}
            priority={!!priority && index < gamesFitPerRow}
            isOverlayEnabled={!isLoggedIn}
          />
        ))}
      </div>
    </div>
  )
}

export default withEnteringAnimation(GamesSection)
