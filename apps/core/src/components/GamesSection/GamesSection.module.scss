@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.container {
  display: flex;
  flex-direction: column;
}

.games {
  display: grid;
  gap: calculate-rem(10px);
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
  justify-content: start;
  grid-template-rows: repeat(var(--desktop-row-count, 1), minmax($game-tile-default-height, auto));
  grid-auto-flow: column;

  @media (max-width: 767px) {
    grid-template-rows: repeat(var(--mobile-row-count, 1), minmax($game-tile-default-height, auto));

    & > :first-child {
      margin-left: calculate-rem(10px);
    }

    & > :last-child {
      margin-right: calculate-rem(10px);
    }
  }

  &::-webkit-scrollbar {
    height: calculate-rem(4px);
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background: $color-surface-600;
    border-radius: calculate-rem(2px);
  }

  &::-webkit-scrollbar-thumb:hover {
    background: $color-surface-700;
  }
}
