import type { Locale } from '@constants/locale'

export enum LayoutEnum {
  HORIZONTAL = 'horizontal',
  VERTICAL = 'vertical',
}

export enum AuthTypeEnum {
  AUTHENTICATED = 'authenticated',
  UNAUTHENTICATED = 'unauthenticated',
}

export enum DynamicallyRenderedContainer {
  CONTAINER = 'container',
}

export enum DynamicallyRenderedWidget {
  EXPERIENCE_PROGRESS_BAR = 'ExperienceProgressBar',
  LANGUAGE_SELECTOR = 'LanguageSelector',
  THEME_TOGGLE_EXPANDED = 'ThemeToggleExpanded',
  PROMOTIONS = 'Promotions',
  WINNERS = 'Winners',
  QUICK_DEPOSIT = 'QuickDeposit',
  SLOTS = 'Slots',
  TILE = 'Tile',
  NAVIGATION_LINK = 'NavigationLink',
  CARD = 'Card',
  LINKS_CARD = 'LinksCard',
  ACTIONS_CONTAINER = 'ActionsContainer',
  NAVIGATION_CARD = 'NavigationCard',
  BANNER = 'Banner',
  ICONS_LIST = 'IconTilesList',
  INFO_CARD = 'InfoCard',
}

export enum DynamicallyRenderedModule {
  SIDE_MENU = 'SideMenu',
  CHAT = 'Chat',
}

export interface IDynamicallyRenderedContentProps {
  locale: Locale
}
