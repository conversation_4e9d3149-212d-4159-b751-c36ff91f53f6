import {
  DynamicallyRenderedWidget,
  DynamicallyRenderedModule,
} from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicallyRenderedChatConfigSchema } from '@modules/chat'
import { DynamicallyRenderedPromotionsConfigSchema } from '@modules/promotions'
import { DynamicallyRenderedSideMenuConfigSchema } from '@modules/sidemenu'
import { DynamicallyRenderedSlotsConfigSchema } from '@modules/slots'
import { DynamicallyRenderedActionsContainerConfigSchema } from '@widgets/ActionsContainerWidget/ActionsContainerWidget.schema'
import { DynamicallyRenderedBannerConfigSchema } from '@widgets/BannerWidget/BannerWidget.schema'
import { DynamicallyRenderedCardConfigSchema } from '@widgets/CardWidget/CardWidget.schema'
import { DynamicallyRenderedExperienceProgressBarConfigSchema } from '@widgets/ExperienceProgressBarWidget/ExperienceProgressBarWidget.schema'
import { DynamicallyRenderedIconTilesListConfigSchema } from '@widgets/IconTilesListWidget/IconTilesListWidget.schema'
import { DynamicallyRenderedLanguageSelectorConfigSchema } from '@widgets/LanguageSelectorWidget/LanguageSelectorWidget.schema'
import { DynamicallyRenderedLinksCardConfigSchema } from '@widgets/LinksCardWidget/LinksCardWidget.schema'
import { DynamicallyRenderedNavigationCardConfigSchema } from '@widgets/NavigationCardWidget/NavigationCardWidget.schema'
import { DynamicallyRenderedNavigationLinkConfigSchema } from '@widgets/NavigationLinkWidget/NavigationLinkWidget.schema'
import { DynamicallyRenderedQuickDepositConfigSchema } from '@widgets/QuickDepositWidget/QuickDepositWidget.schema'
import { DynamicallyRenderedRewardCardConfigSchema } from '@widgets/RewardCardWidget/RewardCardWidget.schema'
import { DynamicallyRenderedMyRewardsConfigSchema } from '@modules/rewards/MyRewards.schema'
import { DynamicallyRenderedThemeToggleExpandedConfigSchema } from '@widgets/ThemeToggleWidget/ThemeToggleWidget.schema'
import { DynamicallyRenderedTileConfigSchema } from '@widgets/TileWidget/TileWidget.schema'
import { DynamicallyRenderedWinnersConfigSchema } from '@widgets/WinnersWidget/WinnersWidget.schema'

export default {
  COMPONENT_TO_SCHEMA_MAP: {
    [DynamicallyRenderedWidget.TILE]: DynamicallyRenderedTileConfigSchema,
    [DynamicallyRenderedWidget.NAVIGATION_LINK]: DynamicallyRenderedNavigationLinkConfigSchema,
    [DynamicallyRenderedWidget.CARD]: DynamicallyRenderedCardConfigSchema,
    [DynamicallyRenderedWidget.LINKS_CARD]: DynamicallyRenderedLinksCardConfigSchema,
    [DynamicallyRenderedWidget.NAVIGATION_CARD]: DynamicallyRenderedNavigationCardConfigSchema,
    [DynamicallyRenderedWidget.ACTIONS_CONTAINER]: DynamicallyRenderedActionsContainerConfigSchema,
    [DynamicallyRenderedWidget.EXPERIENCE_PROGRESS_BAR]: DynamicallyRenderedExperienceProgressBarConfigSchema,
    [DynamicallyRenderedWidget.LANGUAGE_SELECTOR]: DynamicallyRenderedLanguageSelectorConfigSchema,
    [DynamicallyRenderedWidget.THEME_TOGGLE_EXPANDED]: DynamicallyRenderedThemeToggleExpandedConfigSchema,
    [DynamicallyRenderedModule.SIDE_MENU]: DynamicallyRenderedSideMenuConfigSchema,
    [DynamicallyRenderedModule.CHAT]: DynamicallyRenderedChatConfigSchema,
    [DynamicallyRenderedWidget.PROMOTIONS]: DynamicallyRenderedPromotionsConfigSchema,
    [DynamicallyRenderedWidget.WINNERS]: DynamicallyRenderedWinnersConfigSchema,
    [DynamicallyRenderedWidget.QUICK_DEPOSIT]: DynamicallyRenderedQuickDepositConfigSchema,
    [DynamicallyRenderedWidget.SLOTS]: DynamicallyRenderedSlotsConfigSchema,
    [DynamicallyRenderedWidget.BANNER]: DynamicallyRenderedBannerConfigSchema,
    [DynamicallyRenderedWidget.ICONS_LIST]: DynamicallyRenderedIconTilesListConfigSchema,
    [DynamicallyRenderedWidget.REWARD_CARD]: DynamicallyRenderedRewardCardConfigSchema,
    [DynamicallyRenderedWidget.MY_REWARDS]: DynamicallyRenderedMyRewardsConfigSchema,
  },
} as const
