/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$size-xs-radius: $radius-sm;
$size-xs-box: $size-xs;
$size-sm-radius: $radius-md;
$size-sm-box: $size-md;
$size-sm-font: $typography-label-xs;
$size-md-radius: $radius-md;
$size-md-box: $size-md-1;
$size-md-font: $typography-label-xs;
$type-primary-backgorund: $color-primary;
$type-primary-foreground: $color-on-primary;
$type-secondary-background: $color-surface-400;
$type-secondary-foreground: $color-surface-800;
$type-tertiary-background: $color-tertiary;
$type-tertiary-foreground: $color-on-tertaiary;