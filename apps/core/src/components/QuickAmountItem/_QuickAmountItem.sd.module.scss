/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$state-default-background: $color-surface-200;
$state-default-border: $border-width-default solid $color-surface-300;
$state-default-text: $color-surface-800;
$state-active-background: $color-tertiary;
$state-active-border: $border-width-none solid $color-transparent;
$state-active-text: $color-on-tertaiary;
$size-default-radius: $size-xs;
$size-default-padding: $size-xs, $size-xs-1;
$size-default-min-height: $size-2xl;