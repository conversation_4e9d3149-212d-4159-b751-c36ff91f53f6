/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$type-primary-default-background: $color-primary;
$type-primary-default-text: $color-on-primary;
$type-primary-hover-background: $color-primary-focus;
$type-primary-hover-text: $color-on-primary;
$type-primary-disabled-background: $color-surface-400;
$type-primary-disabled-text: $color-surface-700;
$type-secondary-default-background: $color-secondary;
$type-secondary-default-text: $color-on-secondary;
$type-secondary-default-border: $border-width-default solid $color-secondary-border;
$type-tertiary-default-background: $color-tertiary;
$type-tertiary-default-text: $color-on-tertaiary;
$type-tertiary-default-border: $border-width-default solid $color-tertiary-border;
$size-sm-font: $typography-label-sm;
$size-sm-radius: $radius-xs;
$size-sm-gap: $size-xs;
$size-sm-padding: $size-xs $font-size-sm;
$size-sm-min-height: $size-2xl;
$size-md-radius: $size-sm;
$size-md-font: $typography-label-md;
$size-md-gap: $size-xs;
$size-md-padding: $size-sm $size-lg;
$size-md-min-height: $size-3xl-2;
$size-lg-font: $typography-label-md;
$size-lg-radius: $radius-md;
$size-lg-gap: $size-xs;
$size-lg-padding: $size-sm $size-xl;
$size-lg-min-height: $size-3xl;