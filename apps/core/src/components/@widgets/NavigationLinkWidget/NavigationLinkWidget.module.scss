@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.flexStyles {
  display: flex;
  align-items: center;
}

.linkItem {
  @extend .flexStyles;
  justify-content: space-between;
  padding: calculate-rem(12px);
  &.noIconPadding {
    padding: calculate-rem(12px) 0;
  }
}

.iconLabel {
  @extend .flexStyles;
  gap: calculate-rem(8px);
  width: 80%;
}

.label {
  font-size: calculate-rem(15px);
  font-weight: 600;
  color: $color-surface-900;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.badge {
  @extend .flexStyles;
  height: calculate-rem(18px);
  padding: calculate-rem(2.5px) calculate-rem(6px);
  text-transform: uppercase;
  background-color: $color-yellow-400;
  color: $color-on-yellow;
  font-size: calculate-rem(11px);
  font-weight: 700;
  border-radius: calculate-rem(56px);
}
