import type { FC } from 'react'
import { rlF<PERSON>ch<PERSON><PERSON> } from '@/network/server-utils/rl/RLFetchApi'
import { getLicenseByLocale } from '@/utils/locale'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { CardWidgetClient } from '@widgets/CardWidget/CardWidget.client'
import type { DynamicallyRenderedCardConfigType } from '@widgets/CardWidget/CardWidget.schema'

export interface ICardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedCardConfigType
}

const CardWidget: FC<ICardWidgetProps> = async ({ config, locale }) => {
  if (!config || !config.bonusId) {
    return null
  }

  const bonus = await rlFetchApi.getBonus({ id: config.bonusId, locale, license: getLicenseByLocale(locale) })

  return <CardWidgetClient config={config} initialBonus={bonus} />
}

export default CardWidget
