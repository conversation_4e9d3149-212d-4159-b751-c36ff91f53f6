import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedInfoCardConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.INFO_CARD),
  meta: z.object({
    type: z.string(),
    currency: z.string(),
    amount: z.number(),
    imageSrc: z.string().url().optional(),
    backgroundImageSrc: z.string().url().optional(),
    lastWonInfo: z.string().optional(),
  }),
})

export type DynamicallyRenderedInfoCardConfigType = z.infer<typeof DynamicallyRenderedInfoCardConfigSchema>['meta']
