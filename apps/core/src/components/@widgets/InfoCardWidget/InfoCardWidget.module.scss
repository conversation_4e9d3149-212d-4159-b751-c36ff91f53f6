@use '@theme/functions.scss' as *;
@use '@theme/variables.scss' as *;

.infoCard {
  position: relative;
  min-height: calculate-rem(167px);
  border-radius: calculate-rem(8px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  padding: 0 calculate-rem(8px) calculate-rem(32px) calculate-rem(8px);
  text-align: center;

  &.noIconPadding {
    padding: calculate-rem(8px);
  }
}

.background {
  z-index: 0;
  pointer-events: none;
  border-radius: inherit;
}

.image {
  z-index: 0;
  pointer-events: none;
  border-radius: inherit;
  transform: translateY(-20%);
  background-size: cover;
}

.content {
  height: 100%;
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  gap: calculate-rem(8px);
  z-index: 1;
  color: $color-on-tertaiary;
}

.typeCurrency,
.lastWon {
  opacity: 0.5;
}

.typeCurrency {
  font-size: calculate-rem(12px);
  font-weight: 500;
}

.amount {
  font-size: calculate-rem(16px);
  font-weight: 800;
  letter-spacing: calculate-rem(0.16px);
}

.lastWon {
  font-size: calculate-rem(12px);
  font-weight: 500;
}
