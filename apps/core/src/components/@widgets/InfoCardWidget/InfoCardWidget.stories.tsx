import { Locale } from '@constants/locale'
import type { Meta, StoryObj } from '@storybook/react'
import InfoCardWidget from '@widgets/InfoCardWidget/InfoCardWidget'

const meta: Meta<typeof InfoCardWidget> = {
  title: 'Widgets/InfoCardWidget',
  component: InfoCardWidget,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'An Info Card component displaying a type/currency description, amount, and last won info, with optional bg and icon',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    config: {
      type: 'Grand',
      amount: 491098.35,
      currency: 'SC',
      backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-bg.png',
      imageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-image.png',
      lastWonInfo: 'Last won a month ago',
    },
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Configuration for InfoCardWidget including type, amount, currency, images, and last won info.',
    },
    locale: {
      control: 'text',
      description: 'Optional market string for conditional rendering or data logic',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '180px',
          minHeight: '200px',
          padding: '16px',
          borderRadius: '8px',
          position: 'relative',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    config: { table: { disable: true } },
    locale: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'All InfoCardWidget variants showcasing different data and image configurations.',
      },
    },
  },
  render: () => {
    const defaultBackground = 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-bg.png'
    const defaultImage = 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-image.png'

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Content Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Default</h4>
              <InfoCardWidget
                config={{
                  type: 'Grand',
                  currency: 'SC',
                  amount: 491098.35,
                  backgroundImageSrc: defaultBackground,
                  imageSrc: defaultImage,
                  lastWonInfo: 'Last won a month ago',
                }}
                locale={Locale.EN}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Background Image</h4>
              <InfoCardWidget
                config={{
                  type: 'Minor',
                  currency: 'GC',
                  amount: 10235.75,
                  imageSrc: defaultImage,
                  lastWonInfo: 'Last won yesterday',
                }}
                locale={Locale.EN}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Icon Image</h4>
              <InfoCardWidget
                config={{
                  type: 'Major',
                  currency: 'SC',
                  amount: 991234.12,
                  backgroundImageSrc: defaultBackground,
                  lastWonInfo: 'Last won 2 weeks ago',
                }}
                locale={Locale.EN}
              />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive InfoCardWidget with all properties customizable via the config object.',
      },
    },
  },
  args: {
    config: {
      type: 'Minor',
      currency: 'SC',
      amount: 5000.25,
      backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-bg.png',
      imageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/info-card-image.png',
      lastWonInfo: 'Won recently',
    },
  },
  render: args => (
    <div
      style={{
        width: '180px',
        minHeight: '200px',
        padding: '16px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <InfoCardWidget {...args} />
    </div>
  ),
}
