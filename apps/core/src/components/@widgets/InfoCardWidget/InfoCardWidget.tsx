import clsx from 'clsx'
import Image from 'next/image'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import type { DynamicallyRenderedInfoCardConfigType } from '@widgets/InfoCardWidget/InfoCardWidget.schema'
import styles from '@widgets/InfoCardWidget/InfoCardWidget.module.scss'

export interface IInfoCardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedInfoCardConfigType
}

const InfoCardWidget = ({ config }: IInfoCardWidgetProps) => {
  if (!config) return null

  const { type, amount, currency, backgroundImageSrc, imageSrc, lastWonInfo } = config

  return (
    <div className={clsx(styles.infoCard, !imageSrc && styles.noIconPadding)}>
      {backgroundImageSrc ? (
        <Image src={backgroundImageSrc} className={styles.background} quality={100} fill alt={'Info Card background'} />
      ) : null}

      {imageSrc ? (
        <Image
          className={styles.image}
          src={imageSrc}
          quality={100}
          alt={type ? `${type} icon` : 'Info Card icon'}
          width={82}
          height={82}
        />
      ) : null}

      <div className={styles.content}>
        {type && currency ? (
          <p className={styles.typeCurrency}>
            {type} {currency}
          </p>
        ) : null}

        {currency && amount !== undefined && amount !== null ? (
          <p className={styles.amount}>
            {currency} {amount.toLocaleString()}
          </p>
        ) : null}

        {lastWonInfo ? <p className={styles.lastWon}>{lastWonInfo}</p> : null}
      </div>
    </div>
  )
}

export default InfoCardWidget
