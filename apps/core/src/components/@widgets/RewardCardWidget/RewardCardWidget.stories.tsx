import { Locale } from '@constants/locale'
import type { Meta, StoryObj } from '@storybook/react'
import RewardCardWidget from '@widgets/RewardCardWidget/RewardCardWidget'

const meta: Meta<typeof RewardCardWidget> = {
  title: 'Widgets/RewardCardWidget',
  component: RewardCardWidget,
  parameters: {
    layout: 'padded',
    docs: {
      description: {
        component:
          'A Reward Card component displaying a game thumbnail, title, subtitle, and CTA button, with optional background image.',
      },
    },
  },
  tags: ['autodocs'],
  args: {
    config: {
      title: '100 FS at Hercules Son of Zeus',
      subTitle: 'Free spins',
      backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
      thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
      ctaLabel: 'Claim now',
      ctaHref: '#',
    },
  },
  argTypes: {
    config: {
      control: 'object',
      description: 'Configuration for RewardCardWidget including title, subtitle, images, and CTA button.',
    },
    locale: {
      control: 'text',
      description: 'Optional market string for conditional rendering or data logic',
    },
  },
  decorators: [
    Story => (
      <div
        style={{
          width: '259px',
          minHeight: '280px',
          padding: '16px',
          borderRadius: '8px',
          position: 'relative',
        }}>
        <Story />
      </div>
    ),
  ],
}

export default meta
type Story = StoryObj<typeof meta>

export const Variants: Story = {
  argTypes: {
    config: { table: { disable: true } },
    locale: { table: { disable: true } },
  },
  parameters: {
    docs: {
      description: {
        story: 'All RewardCardWidget variants showcasing different data and image configurations.',
      },
    },
  },
  render: () => {
    const defaultBackground = 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png'
    const defaultThumbnail = 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png'

    return (
      <div style={{ display: 'flex', flexDirection: 'column', gap: '3rem' }}>
        <div>
          <h3 style={{ marginBottom: '2rem', fontSize: '1.2rem', fontWeight: 'bold' }}>Content Variations</h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '2rem' }}>
            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>Default</h4>
              <RewardCardWidget
                config={{
                  title: '100 FS at Hercules Son of Zeus',
                  subTitle: 'Free spins',
                  backgroundImageSrc: defaultBackground,
                  thumbnailSrc: defaultThumbnail,
                  ctaLabel: 'Claim now',
                  ctaHref: '#',
                }}
                locale={Locale.EN}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Background Image</h4>
              <RewardCardWidget
                config={{
                  title: '50 GC at Wild Wild West',
                  subTitle: 'Golden coins',
                  thumbnailSrc: defaultThumbnail,
                  ctaLabel: 'Claim now',
                  ctaHref: '#',
                }}
                locale={Locale.EN}
              />
            </div>

            <div>
              <h4 style={{ fontSize: '1rem', fontWeight: '600', marginBottom: '1rem' }}>No Thumbnail Image</h4>
              <RewardCardWidget
                config={{
                  title: '200 FS at Mega Moolah',
                  subTitle: 'Free spins',
                  backgroundImageSrc: defaultBackground,
                  ctaLabel: 'Claim now',
                  ctaHref: '#',
                }}
                locale={Locale.EN}
              />
            </div>
          </div>
        </div>
      </div>
    )
  },
}

export const Playground: Story = {
  parameters: {
    docs: {
      description: {
        story: 'Interactive RewardCardWidget with all properties customizable via the config object.',
      },
    },
  },
  args: {
    config: {
      title: '150 FS at Book of Dead',
      subTitle: 'Free spins',
      backgroundImageSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-bg.png',
      thumbnailSrc: 'https://luckyspins-staging4.imgix.net/sweep-rush/reward-thumbnail.png',
      ctaLabel: 'Claim now',
      ctaHref: '#',
    },
  },
  render: args => (
    <div
      style={{
        width: '259px',
        minHeight: '200px',
        padding: '16px',
        borderRadius: '8px',
        position: 'relative',
      }}>
      <RewardCardWidget {...args} />
    </div>
  ),
}
