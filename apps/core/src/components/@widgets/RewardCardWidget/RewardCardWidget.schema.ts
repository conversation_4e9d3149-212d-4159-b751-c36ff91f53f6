import { z } from 'zod'
import { DynamicallyRenderedContentBaseConfigSchema } from '@components/DynamicContentRenderer/DynamicContentRenderer.schema'
import { DynamicallyRenderedWidget } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'

export const DynamicallyRenderedRewardCardConfigSchema = DynamicallyRenderedContentBaseConfigSchema.extend({
  component: z.literal(DynamicallyRenderedWidget.REWARD_CARD),
  meta: z.object({
    title: z.string(),
    subTitle: z.string(),
    backgroundImageSrc: z.string().url().optional(),
    thumbnailSrc: z.string().url().optional(),
    ctaLabel: z.string().optional(),
    ctaHref: z.string().optional(),
    ctaActionKey: z.string().optional(),
  }),
})

export type DynamicallyRenderedRewardCardConfigType = z.infer<typeof DynamicallyRenderedRewardCardConfigSchema>['meta']
