'use client'

import Image from 'next/image'
import { Button } from '@components/Button'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicLink } from '@components/DynamicLink'
import type { DynamicallyRenderedRewardCardConfigType } from '@widgets/RewardCardWidget/RewardCardWidget.schema'
import styles from '@widgets/RewardCardWidget/RewardCardWidget.module.scss'
import { useIsMobile } from '../../../../../../packages/hooks/useMobile'

export interface IRewardCardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedRewardCardConfigType
}

const RewardCardWidget = ({ config }: IRewardCardWidgetProps) => {
  const isMobile = useIsMobile()

  if (!config) return null

  const { title, subTitle, backgroundImageSrc, thumbnailSrc, ctaHref, cta<PERSON>abel, ctaActionKey } = config

  return (
    <div className={styles.rewardCard} aria-label="Reward Card">
      {backgroundImageSrc ? (
        <Image src={backgroundImageSrc} className={styles.background} quality={100} fill alt="Reward Card background" />
      ) : null}

      {thumbnailSrc ? (
        <div className={styles.thumbnail}>
          <Image src={thumbnailSrc} quality={100} alt={`${title || 'Reward'} thumbnail`} width={68} height={68} />
        </div>
      ) : null}

      <div className={styles.content}>
        <div className={styles.titleContainer}>
          {title ? <p className={styles.title}>{title}</p> : null}
          {subTitle ? <p className={styles.subTitle}>{subTitle}</p> : null}
        </div>

        {ctaLabel ? (
          <div className={styles.ctaButton}>
            <Button
              as={ctaHref ? DynamicLink : 'button'}
              {...(ctaHref ? { href: ctaHref } : {})}
              label={ctaLabel}
              color="primary"
              size={isMobile ? 'sm' : 'md'}
              fullWidth={!isMobile}
              isTruncated
            />
          </div>
        ) : null}
      </div>
    </div>
  )
}

export default RewardCardWidget
