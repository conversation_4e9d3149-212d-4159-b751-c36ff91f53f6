'use client'
import type { FC } from 'react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { Button } from '@components/Button'
import { Card, CardHeader, CardTitle, CardContent } from '@components/Card'
import type { IDynamicallyRenderedContentProps } from '@components/DynamicContentRenderer/DynamicContentRenderer.types'
import { DynamicLink } from '@components/DynamicLink'
import type { DynamicallyRenderedLinksCardConfigType } from '@widgets/LinksCardWidget/LinksCardWidget.schema'
import styles from '@widgets/LinksCardWidget/LinksCardWidget.module.scss'

export interface ILinksCardWidgetProps extends IDynamicallyRenderedContentProps {
  config: DynamicallyRenderedLinksCardConfigType
}
const LinksCardWidget: FC<ILinksCardWidgetProps> = ({ config, locale }) => {
  const { headline, backgroundUrl, items } = config
  const { resolvedTheme: theme } = useTheme()
  const isDarkTheme = theme ? theme === 'dark' : true

  return (
    <Card className={styles.socialMediaCard}>
      {backgroundUrl ? (
        <Image
          src={backgroundUrl}
          loader={({ src, width }) => src + `?dpr=2&w=${width}&q=95`}
          className={styles.backgroundImage}
          quality={95}
          fill
          alt="card-background"
          sizes="15vw"
          style={{
            objectFit: 'cover',
          }}
        />
      ) : null}

      <CardHeader className={styles.header}>
        <CardTitle as="h2" className={styles.title}>
          {headline}
        </CardTitle>
      </CardHeader>

      <CardContent className={styles.content}>
        {items.map(({ icon, icon_dark, alt_text, href }) => (
          <Button
            as={DynamicLink}
            key={alt_text}
            href={href}
            target="_blank"
            rel="noopener noreferrer"
            aria-label={alt_text}
            color="overlay"
            size="sm"
            icon={isDarkTheme ? icon_dark : icon}
          />
        ))}
      </CardContent>
    </Card>
  )
}

export default LinksCardWidget
