/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$size-default-radius: $radius-full;
$size-default-min-width: $size-3xl-3;
$size-default-padding: $size-xxs;
$size-default-toggle-box: $size-lg;
$size-default-toggle-radius: $radius-full;
$size-default-toggle-icon: $icon-size-xxs;
$state-on-default-background: $color-surface-400;
$state-on-default-toggle-background: $color-surface-800;
$state-on-default-toggle-foreground: $color-surface-1000;
$state-off-default-background: $color-tertiary;
$state-off-default-toggle-background: $color-on-tertaiary;
$state-off-default-toggle-foreground: $color-tertiary;