/**
 * Do not edit directly, this file was auto-generated.
 */
@use '@theme/style-dictionary' as *;
$state-unchecked-default-background: $color-shade-100;
$state-unchecked-default-border: $border-width-default solid $color-shade-400;
$state-unchecked-hover-border: $color-shade-500;
$state-unchecked-active-border: $color-shade-500;
$state-unchecked-disabled-background: $color-shade-200;
$state-unchecked-disabled-border: $color-shade-200;
$state-checked-default-background: $color-tertiary;
$state-checked-default-foreground: $color-on-tertaiary;
$state-checked-default-border: $color-transparent;
$size-default-radius: $size-xs;