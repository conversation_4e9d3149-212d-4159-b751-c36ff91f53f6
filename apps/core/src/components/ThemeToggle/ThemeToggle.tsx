'use client'
import React from 'react'
import { clsx } from 'clsx'
import { useTheme } from 'next-themes'
import { MoonIcon } from '@/ui/icons/MoonIcon'
import { SunIcon } from '@/ui/icons/SunIcon'
import { Switch } from '@heroui/switch'
import { useIsMounted } from '@repo/hooks/useIsMounted'
import styles from '@components/ThemeToggle/ThemeToggle.module.scss'

const ThemeToggle: React.FC = () => {
  const isMounted = useIsMounted()
  const { resolvedTheme: theme, setTheme } = useTheme()
  const updateTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light')
  }

  return (
    <Switch
      aria-label="Toggle theme"
      defaultSelected
      className={clsx(styles.toggler)}
      color="success"
      size="sm"
      thumbIcon={
        isMounted
          ? ({ className }) => {
              if (theme === 'light') {
                return <SunIcon className={className} />
              }
              if (theme === 'dark') {
                return <MoonIcon className={className} />
              }
              return null
            }
          : undefined
      }
      onChange={updateTheme}
    />
  )
}

export default ThemeToggle
