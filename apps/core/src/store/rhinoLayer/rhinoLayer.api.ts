import { axiosBaseQuery } from '@/network/baseQuery'
import { getRLRequestParamsByLocale } from '@/utils/locale'
import { PromotionsSettings, getPromotionsTransformResponse } from '@modules/promotions'
import { createApi } from '@reduxjs/toolkit/query/react'
import type { IBonus, IGetActiveBonusesForEcrProps, IGetBonusProps } from '@repo/types/api/rl/bonus'
import type { IRlResponse, IRlResponseSuccess } from '@repo/types/api/rl/index'
import type { IGetPromotionsProps, IRlPromotionsResponse } from '@repo/types/api/rl/promotions'

export const rhinolayerApi = createApi({
  reducerPath: 'api.rhinolayer',
  baseQuery: axiosBaseQuery({
    baseUrl: '/api/rl',
    prepareHeaders(headers) {
      headers.set('Accept', 'application/json;v=3')
      return headers
    },
    skipToastForErrorCodes: [400],
  }),
  keepUnusedDataFor: 30,
  tagTypes: ['PROMOTIONS', 'BONUS', 'USER_BONUSES'],
  endpoints: builder => ({
    getPromotions: builder.query<IRlPromotionsResponse | undefined, IGetPromotionsProps>({
      query: ({ locale, license, tags, ids, isLoggedIn, type = 0, page = 1, limit = 20, affiliateId }) => {
        const rlParams = getRLRequestParamsByLocale(locale)

        return {
          url: '/promotions',
          params: {
            ...rlParams,
            license,
            tags: (tags || []).join(','),
            ...(ids?.length && { ids: (ids || []).join(',') }),
            page,
            limit,
            type,
            affiliateIds: affiliateId,
          },
          headers: {
            Accept: 'application/json;v=3',
          },
        }
      },
      transformResponse: (response: IRlResponseSuccess<IRlPromotionsResponse>, meta, args) => {
        return getPromotionsTransformResponse(response, { isLoggedIn: args.isLoggedIn || false })
      },
      providesTags: ['PROMOTIONS'],
    }),
    getPromotionsPaginated: builder.infiniteQuery<IRlPromotionsResponse, Omit<IGetPromotionsProps, 'page'>, number>({
      query: arg => {
        const { locale, license, tags, ids, type, limit, affiliateId, isLoggedIn } = arg.queryArg
        const page = arg.pageParam || 0
        const rlParams = getRLRequestParamsByLocale(locale)
        console.log(
          `Fetching promotions for market: ${rlParams.market}, iso2: ${rlParams.iso2}, 
          license: ${license}, tags: ${tags}, ids: ${ids}, isLoggedIn: ${isLoggedIn}, 
          type: ${type}, page: ${page}, limit: ${limit}, affiliateId: ${affiliateId}`,
        )

        return {
          url: '/promotions',
          params: {
            ...rlParams,
            license,
            tags: (tags || []).join(','),
            ...(ids?.length && { ids: (ids || []).join(',') }),
            page,
            limit,
            type,
            affiliateIds: affiliateId,
          },
          headers: {
            Accept: 'application/json;v=3',
          },
        }
      },
      transformResponse: (response: IRlResponseSuccess<IRlPromotionsResponse>, meta, args) => {
        return getPromotionsTransformResponse(response, { isLoggedIn: args.queryArg.isLoggedIn })
      },
      infiniteQueryOptions: {
        initialPageParam: 1,
        getNextPageParam: (lastPage, allPages, lastPageParam) => {
          const perPage = PromotionsSettings.PER_PAGE
          const currentItemsCount = lastPage.currentItemsCount || 0

          if (currentItemsCount < perPage) {
            return undefined
          }
          return lastPageParam + 1
        },
      },
      providesTags: ['PROMOTIONS'],
    }),
    getBonus: builder.query<any | undefined, IGetBonusProps>({
      query: ({ id, locale, license }) => {
        const rlParams = getRLRequestParamsByLocale(locale)
        return {
          url: '/bonus',
          params: {
            ...rlParams,
            license,
            id,
          },
        }
      },
      transformResponse: (response: IRlResponse<IBonus>) => response?.data,
      providesTags: (result, error, { id }) => ['BONUS', { type: 'BONUS', id }],
    }),
    getActiveBonusesForEcr: builder.query<any, IGetActiveBonusesForEcrProps>({
      query: ({ ecrId, locale, license }) => {
        const rlParams = getRLRequestParamsByLocale(locale)
        return {
          url: '/ecr/bonuses',
          params: {
            ...rlParams,
            ecrId,
            license,
          },
        }
      },
      transformResponse: (response: IRlResponse<IBonus[]>) => response?.data,
      providesTags: ['USER_BONUSES'],
    }),
  }),
})

export const {
  useGetPromotionsQuery,
  useLazyGetPromotionsQuery,
  useGetPromotionsPaginatedInfiniteQuery,
  useGetBonusQuery,
  useGetActiveBonusesForEcrQuery,
} = rhinolayerApi
