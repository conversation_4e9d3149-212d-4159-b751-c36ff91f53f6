import type { PlopTypes } from '@turbo/gen'

const derivedProjectsPaths = ['luckyone', 'luckytwo'] // NOTE: Add new project directory whenever a new project is created

// TODO: Extend to support for dynamic routes

const pathToPageName = (input: string) => {
  const isNestedPath = input.includes('/')
  return input
    .split(isNestedPath ? /[-/]/ : '-')
    .map(part => part.charAt(0).toUpperCase() + part.slice(1))
    .join('')
}

export default function generator(plop: PlopTypes.NodePlopAPI): void {
  plop.setGenerator('page', {
    description: 'create a new app page',
    prompts: [
      {
        type: 'input',
        name: 'path',
        message: 'Choose a path for the page',
        validate: input => {
          if ((input || '').trim().length === 0) {
            return 'Path is required!'
          }
          const pathRegex = /^[a-zA-Z]+([/-][a-zA-Z]+)*$/
          if (!pathRegex.test(input)) {
            return 'Invalid path! Enter a valid path value using letters and hyphens. Nested routes are allowed using forward slashes as separators.'
          }
          return true
        },
        filter: input => {
          return input.toLowerCase()
        },
      },
      {
        type: 'list',
        name: 'auth',
        message: 'Choose an auth for the page:',
        choices: ['authenticated', '/'],
        default: 'both',
        filter: input => {
          if (input === 'authenticated') {
            return '/(authenticated)/'
          }
          return '/'
        },
      },
    ],
    actions: data => {
      let actions: PlopTypes.ActionType[] = [
        {
          type: 'add',
          path: 'apps/core/src/app/[locale]{{auth}}{{path}}/page.tsx',
          templateFile: 'templates/page/page.hbs',
          data: { ...data, name: pathToPageName(data?.path) },
        },
        {
          type: 'append',
          path: 'apps/core/src/config/serverConfig.ts',
          pattern: /(?<=publicRoutes:\s*\[[^\]]*)'(?!.*')/, // find the last "'" before the first "]" after "publicRoutes:"
          separator: ', ',
          template: "'/{{path}}'",
        },
      ]
      derivedProjectsPaths.forEach((derivedProjectPath, index) => {
        actions.splice(index + 1, 0, {
          type: 'add',
          path: 'apps/' + derivedProjectPath + '/src/app/[locale]{{auth}}{{path}}/page.tsx',
          templateFile: 'templates/page/page-reexport.hbs',
        })
      })

      return actions
    },
  })
}
