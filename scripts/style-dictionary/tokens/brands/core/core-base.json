{"radius": {"none": {"value": "0rem", "type": "dimension"}, "xxs": {"value": "0.25rem", "type": "dimension"}, "xs": {"value": "0.375rem", "type": "dimension"}, "sm": {"value": "0.5rem", "type": "dimension"}, "md": {"value": "0.75rem", "type": "dimension"}, "lg": {"value": "1rem", "type": "dimension"}, "full": {"value": "1000rem", "type": "dimension"}}, "font": {"family": {"primary": {"value": "Roboto", "type": "fontFamilies"}, "secondary": {"value": "{font.family.primary}", "type": "fontFamilies"}}, "size": {"xs": {"value": "0.75rem", "type": "dimension"}, "sm": {"value": "0.875rem", "type": "dimension"}, "md": {"value": "1rem", "type": "dimension"}, "lg": {"value": "1.125rem", "type": "dimension"}, "xl": {"value": "1.25rem", "type": "dimension"}, "xl-1": {"value": "1.375rem", "type": "dimension"}, "2xl": {"value": "1.5rem", "type": "dimension"}, "3xl": {"value": "2rem", "type": "dimension"}, "4xl": {"value": "3rem", "type": "dimension"}}, "line-height": {"tight": {"value": "100%", "type": "lineHeights"}, "normal": {"value": "120%", "type": "lineHeights"}}, "weight": {"baseline": {"value": "500", "type": "fontWeights"}, "emphasized": {"value": "800", "type": "fontWeights"}}}, "typography": {"label": {"xs": {"value": {"fontFamily": "{font.family.primary}", "fontSize": "{font.size.xs}", "lineHeight": "{font.line-height.tight}", "fontWeight": "{font.weight.emphasized}"}, "type": "typography"}, "sm": {"value": {"fontFamily": "{font.family.primary}", "fontSize": "{font.size.sm}", "lineHeight": "{font.line-height.tight}", "fontWeight": "{font.weight.emphasized}"}, "type": "typography"}, "md": {"value": {"fontFamily": "{font.family.primary}", "fontSize": "{font.size.md}", "lineHeight": "{font.line-height.tight}", "fontWeight": "{font.weight.emphasized}"}, "type": "typography"}}, "body": {"baseline": {"xs": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.baseline}", "fontSize": "{font.size.xs}", "lineHeight": "{font.line-height.normal}"}, "type": "typography"}, "sm": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.baseline}", "fontSize": "{font.size.sm}", "lineHeight": "{font.line-height.normal}"}, "type": "typography"}, "md": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.baseline}", "fontSize": "{font.size.md}", "lineHeight": "{font.line-height.normal}"}, "type": "typography"}}}, "headline": {"xs": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.emphasized}", "fontSize": "{font.size.lg}", "lineHeight": "{font.line-height.tight}"}, "type": "typography"}, "sm": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.emphasized}", "fontSize": "{font.size.xl-1}", "lineHeight": "{font.line-height.tight}"}, "type": "typography"}, "md": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.emphasized}", "fontSize": "{font.size.2xl}", "lineHeight": "{font.line-height.tight}"}, "type": "typography"}, "lg": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.emphasized}", "fontSize": "{font.size.3xl}", "lineHeight": "{font.line-height.tight}"}, "type": "typography"}, "xl": {"value": {"fontFamily": "{font.family.primary}", "fontWeight": "{font.weight.emphasized}", "fontSize": "{font.size.4xl}", "lineHeight": "{font.line-height.tight}"}, "type": "typography"}}}, "size": {"xxs": {"value": "0.25rem", "type": "dimension"}, "xxs-1": {"value": "0.375rem", "type": "dimension"}, "xs": {"value": "0.5rem", "type": "dimension"}, "xs-1": {"value": "0.625rem", "type": "dimension"}, "sm": {"value": "0.75rem", "type": "dimension"}, "md": {"value": "1rem", "type": "dimension"}, "md-1": {"value": "1.125rem", "type": "dimension"}, "lg": {"value": "1.25rem", "type": "dimension"}, "xl": {"value": "1.5rem", "type": "dimension"}, "2xl": {"value": "2rem", "type": "dimension"}, "2xl-1": {"value": "2.25rem", "type": "dimension"}, "3xl-2": {"value": "2.5rem", "type": "dimension"}, "3xl-3": {"value": "2.75rem", "type": "dimension"}, "3xl": {"value": "3rem", "type": "dimension"}, "4xl": {"value": "4rem", "type": "dimension"}, "5xl": {"value": "5rem", "type": "dimension"}}, "border": {"width": {"none": {"value": "0px", "type": "dimension"}, "default": {"value": "1px", "type": "dimension"}, "heavy": {"value": "2px", "type": "dimension"}}}, "icon": {"size": {"xxs": {"value": "0.875rem", "type": "dimension"}, "xs": {"value": "1rem", "type": "dimension"}, "sm": {"value": "1.25rem", "type": "dimension"}, "md": {"value": "1.5rem", "type": "dimension"}}}}